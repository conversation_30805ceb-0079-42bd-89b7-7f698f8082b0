from datetime import datetime
from thirdparty_order_fetcher import (
    TaobaoOrderFetcher, 
    JDOrderFetcher,
    XiaohongshuOrderFetcher  # 新增导入
)

def print_orders(platform_name: str, orders):
    """打印订单信息的辅助函数"""
    print(f"\n=== {platform_name}订单 ===")
    print(f"获取到 {len(orders)} 个订单")
    
    for order in orders:
        print("\n订单详情:")
        print(f"订单ID: {order.order_id}")
        print(f"支付状态: {order.payment_status}")
        print(f"支付时间: {order.payment_time}")
        print(f"发货时间: {order.shipping_time}")
        print("-" * 50)

def main():
    # 获取当前年月
    year = 2025
    month = 4
    
    try:
        # 新增小红书订单测试
        # xhs_fetcher = XiaohongshuOrderFetcher()
        # xhs_orders = xhs_fetcher.fetch_orders(year, month)
        # print_orders("小红书", xhs_orders)

        # # 测试淘宝订单获取
        taobao_fetcher = TaobaoOrderFetcher()
        taobao_orders = taobao_fetcher.fetch_orders(year, month)
        # print_orders("淘宝", taobao_orders)
        #
        # # 测试京东订单获取
        # jd_fetcher = JDOrderFetcher()
        # jd_orders = jd_fetcher.fetch_orders(year, month)
        # print_orders("京东", jd_orders)

    except ValueError as e:
        print(f"参数错误: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main() 