#!/usr/bin/env python3
"""
测试交易结束时间功能
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_folder_utils import FolderManager
from db_manager import OrdersManager, TableFields
from db_manager.order_status import OrderStatus
from shop_manager.constants import PlatformType, PlatformName


def test_end_time_feature():
    """测试交易结束时间功能"""
    
    print("=== 测试交易结束时间功能 ===")
    
    # 初始化订单管理器
    db_path = FolderManager.get_db_file_path()
    orders_manager = OrdersManager(db_path)
    
    # 测试数据
    test_order_id = "test_end_time_001"
    end_time = datetime.now()
    end_time_timestamp = int(end_time.timestamp())
    
    # 插入测试订单数据
    test_data = {
        TableFields.ORDER_ID: test_order_id,
        TableFields.PLATFORM_TYPE: PlatformType.TAOBAO.value,
        TableFields.PLATFORM_NAME: PlatformName.TB_BAINIANG_WINE.value,
        TableFields.MAIN_ORDER_ID: test_order_id,
        TableFields.ORDER_STATUS: OrderStatus.COMPLETED,
        TableFields.SHIPPING_TIME: end_time_timestamp - 86400,  # 发货时间比结束时间早1天
        TableFields.END_TIME: end_time_timestamp,  # 交易结束时间
        TableFields.MERCHANT_RECEIVABLE: 100.00,
        TableFields.ORDER_AMOUNT: 120.00,
        TableFields.RECEIVABLE_AMOUNT: 100.00,
        TableFields.PAYMENT_CHANNEL: "alipay",
    }
    
    try:
        # 删除可能存在的测试数据
        orders_manager.execute_query("DELETE FROM sales_data WHERE order_id = ?", (test_order_id,))
        
        # 插入测试数据
        print(f"插入测试订单: {test_order_id}")
        success = orders_manager.insert_data(test_data)
        if not success:
            print("插入测试数据失败")
            return False
        
        # 查询测试数据
        print("查询测试数据...")
        result = orders_manager.query_data(filters={'order_id': test_order_id})
        
        if 'error' in result:
            print(f"查询失败: {result['error']}")
            return False
        
        data = result.get('data', [])
        if not data:
            print("未找到测试数据")
            return False
        
        order_data = data[0]
        print(f"订单ID: {order_data.get('order_id')}")
        print(f"平台类型: {order_data.get('platform_type')}")
        print(f"平台名称: {order_data.get('platform_name')}")
        print(f"订单状态: {order_data.get('order_status')}")
        print(f"发货时间: {order_data.get('shipping_time')}")
        print(f"交易结束时间: {order_data.get('end_time')}")
        
        # 验证end_time字段
        stored_end_time = order_data.get('end_time')
        if stored_end_time is None:
            print("❌ end_time字段为空")
            return False
        
        if stored_end_time != end_time_timestamp:
            print(f"❌ end_time值不匹配: 期望 {end_time_timestamp}, 实际 {stored_end_time}")
            return False
        
        print("✅ end_time字段测试通过")
        
        # 测试时间戳转换
        converted_time = datetime.fromtimestamp(stored_end_time)
        print(f"转换后的时间: {converted_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 清理测试数据
        orders_manager.execute_query("DELETE FROM sales_data WHERE order_id = ?", (test_order_id,))
        print("清理测试数据完成")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False


def test_table_structure():
    """测试表结构是否包含end_time字段"""
    
    print("\n=== 测试表结构 ===")
    
    db_path = FolderManager.get_db_file_path()
    orders_manager = OrdersManager(db_path)
    
    try:
        # 查询表结构
        result = orders_manager.execute_query("PRAGMA table_info(sales_data)")
        
        columns = [row[1] for row in result]  # 第二列是字段名
        print(f"表字段: {columns}")
        
        if 'end_time' in columns:
            print("✅ end_time字段存在")
            return True
        else:
            print("❌ end_time字段不存在")
            return False
            
    except Exception as e:
        print(f"查询表结构失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("开始测试交易结束时间功能...")
    
    # 测试表结构
    structure_ok = test_table_structure()
    
    if structure_ok:
        # 测试功能
        feature_ok = test_end_time_feature()
        
        if feature_ok:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 功能测试失败")
            sys.exit(1)
    else:
        print("\n❌ 表结构测试失败，请先运行迁移脚本")
        print("运行命令: python db_manager/migrate_add_end_time.py")
        sys.exit(1)
