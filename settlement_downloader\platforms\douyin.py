from datetime import datetime
from typing import Dict, Any

import shop_manager
from db_manager import TableFields
from db_manager.base_db_manager import BaseDBManager
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop

logger = logging.getLogger(__name__)


class Douyin(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.DOUYIN.value

    def run(self, shop: Shop, db: BaseDBManager, start_date: datetime, end_date: datetime) -> list[
        Dict[TableFields, Any]]:

        query = """
        SELECT COUNT(*) FROM settlements 
        WHERE platform_id = ? 
        AND settlement_time >= ? 
        AND settlement_time <= ?
        """
        result = db.execute_query(
            query, (shop.shop_id, start_date, end_date))
        if result and result[0][0] > 0:
            logger.info(
                f"店铺 {shop.shop_name} 的 {start_date.strftime('%Y-%m')} 月数据已存在，跳过处理")
            return []

        data = []

        # 请求接口拿到下载地址
        url = 'https://callback.vinehoo.com/live-order-sync/liveorders/v3/bill_details'
        post_data = {
            'shop_id': shop.shop_id,
            'startTime': start_date.strftime("%Y-%m-%d %H:%M:%S"),
            'endTime': end_date.strftime("%Y-%m-%d %H:%M:%S"),
        }

        resp = requests.post(url, post_data, timeout=30).json()
        if resp.get('error_code', -1) != 0:
            raise PlatformRunError(f"响应:{resp}")

        download_url = resp.get('data', '')
        if not download_url:
            raise PlatformRunError(f"下载地址为空：响应:{resp}")

        # 下载Excel文件
        excel_response = requests.get(download_url)
        if excel_response.status_code != 200:
            raise PlatformRunError(
                f"下载Excel文件失败: status_code={excel_response.status_code}")

        # 读取Excel文件内容
        try:
            # 读取所有sheet
            df = pd.read_csv(io.BytesIO(excel_response.content))
            # 处理每一行数据
            for _, row in df.iterrows():
                settlement_type = "收入"

                data.append({
                    TableFields.ORDER_ID: str(row['shop_order_id']),
                    TableFields.PLATFORM_ID: shop.shop_id,
                    TableFields.PLATFORM_NAME: shop.shop_name,
                    TableFields.PLATFORM_TYPE: self.platform_type(),
                    TableFields.SETTLEMENT_TIME: row['settle_time'],
                    TableFields.SETTLEMENT_AMOUNT: float(row['total_income']),
                    TableFields.SETTLEMENT_TYPE: settlement_type,
                    TableFields.SETTLEMENT_CHANNEL: "",
                })

        except Exception as e:
            raise PlatformRunError(f"处理Excel文件失败: {str(e)}")
        if len(data) > 0:
            if not db.insert_batch_data(data):
                raise PlatformRunError(f"保存数据失败")

        return data
