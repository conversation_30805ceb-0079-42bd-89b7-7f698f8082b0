import logging
import sqlite3
from datetime import datetime
from typing import List

from db_manager.base_db_manager import BaseDBManager
from .base import SettlementPlatform
from .platform_shop_config import PlatformShopConfig
from .utils import validate_date_range

logger = logging.getLogger(__name__)


class SettlementDownloader:
    """结算单下载器，管理多个平台"""

    def __init__(self, db: BaseDBManager, platforms: List[SettlementPlatform]):
        self.db = db
        self.platforms = platforms

    def download_settlements(
            self,
            start_date: datetime,
            end_date: datetime,
            max_attempts: int = 1
    ) -> None:
        """下载指定时间范围内的结算单"""

        # 清空 settlements 表
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM settlements")
            conn.commit()
            conn.close()
            logger.info("settlements表已清空")
        except Exception as e:
            logger.error(f"清空settlements表失败: {str(e)}")
            if conn:
                conn.close()
            raise  # 如果清空失败，抛出异常不继续执行

        # 验证日期范围
        validate_date_range(start_date, end_date)

        for platform in self.platforms:
            logger.info(f"平台: {platform.platform_type()} 开始处理------\n")
            try:
                self._process_platform(platform, start_date, end_date, max_attempts)
            except Exception as e:
                logger.error(f"Error 平台 {platform.platform_type} : {str(e)}")

    def _process_platform(
            self,
            platform: SettlementPlatform,
            start_date: datetime,
            end_date: datetime,
            max_attempts: int
    ) -> None:
        """处理单个平台的下载逻辑"""
        # 拿到所有店铺
        shops = PlatformShopConfig().get_shops_by_platform(platform.platform_type())
        for shop in shops:
            logger.info(f"店铺:[{shop.shop_name}] 开始处理")
            platform.run(shop, self.db, start_date, end_date)
            logger.info(f"店铺:[{shop.shop_name}] 处理完成\n")
