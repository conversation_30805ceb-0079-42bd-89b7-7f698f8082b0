from abc import ABC, abstractmethod
from typing import Dict, Any

from db_manager import TableFields
from db_manager.base_db_manager import BaseDBManager
from settlement_downloader.platform_shop_config import Shop
from datetime import datetime


class SettlementPlatform(ABC):
    """结算单下载平台的抽象基类"""

    @abstractmethod
    def run(self, shop: Shop, db: BaseDBManager, start_date: datetime, end_date: datetime) -> list[Dict[TableFields, Any]]:
        """处理结算单"""
        pass

    @abstractmethod
    def platform_type(self) -> str:
        """返回平台类型"""
        pass
