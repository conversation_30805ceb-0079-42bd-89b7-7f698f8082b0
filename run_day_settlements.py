#!/usr/bin/env python
# coding: utf-8

"""
根据结算记录更新销售数据中的应收金额和待回款金额
"""

import os
import sys
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.append(root_dir)

# 导入需要的模块
from data_folder_utils import FolderManager
from db_manager import SettlementManager
from settlement_downloader.downloader import SettlementDownloader
from settlement_downloader.platforms.xiaohongshu import Xiaohongshu
from settlement_downloader.platforms.taobao import Taobao
from vinehoo_order_fetcher.order_fetcher import OrderFetcher


def check_environment():
    """检查必需的环境变量是否已设置"""
    required_env = ['DB_URL']
    missing_env = [var for var in required_env if not os.getenv(var)]
    
    if missing_env:
        print(f"\n错误：缺少必需的环境变量: {', '.join(missing_env)}")
        print("\n请在.env文件中包含这些变量\n")
        return False
    return True


def main():
    """执行月度数据处理流程"""
    # 加载环境变量
    load_dotenv()
    
    # 检查环境变量
    if not check_environment():
        return 1
    
    # 设置要处理的年月
    today = datetime.today()
    last_month = today.replace(day=1) - timedelta(days=1)
    year = last_month.year
    month = last_month.month

    print(f"\n开始处理 {year}年{month}月 的数据...")

    try:
        # 1. 下载结算单数据
        print("\n=== 步骤1: 下载结算单数据 ===")
        db_path = FolderManager.get_db_file_path()
        db_settlement = SettlementManager(db_path)

        downloader = SettlementDownloader(
            db_settlement,
            platforms=[Xiaohongshu()]
            # platforms=[Xiaohongshu(),Taobao()] # todo.....
        )

        # 设置时间范围
        start_date = datetime(year, month, 1)
        start_date = datetime(year, 1, 1)

        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)
        end_date = datetime(year, 6, 1)
        end_date = end_date.replace(microsecond=0) - timedelta(seconds=1)

        # 执行下载
        downloader.download_settlements(
            start_date=start_date,
            end_date=end_date,
            max_attempts=3
        )

        # 2. 更新销售数据的应收金额和待回款金额
        print("\n=== 步骤2: 更新销售数据的应收金额和待回款金额 ===")
        print("="*80)
        print(f"开始更新销售数据的应收金额和待回款金额 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 初始化OrderFetcher对象
        order_fetcher = OrderFetcher()
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用更新方法
        updated_count = order_fetcher.update_receivable_from_settlements()
        
        # 计算运行时间
        execution_time = time.time() - start_time
        
        print("\n结果摘要:")
        print(f"更新记录数: {updated_count}")
        print(f"执行时间: {execution_time:.2f} 秒")
        print("="*80)
        
        return 0
        
    except ValueError as e:
        print(f"\n错误：{str(e)}")
        print("="*80)
        return 1
    except Exception as e:
        print(f"\n执行失败: {str(e)}")
        print("="*80)
        return 1


if __name__ == "__main__":
    sys.exit(main())