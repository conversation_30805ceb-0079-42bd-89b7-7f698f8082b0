#!/usr/bin/env python3
"""
检查MySQL表中的end_time字段
"""

import os
import sys
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_mysql_table_structure():
    """检查MySQL表结构"""
    
    # 构建数据库连接URL
    db_host = os.getenv('DB_HOST')
    db_port = os.getenv('DB_PORT', '3306')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_name = os.getenv('DB_NAME')
    
    if not all([db_host, db_user, db_password, db_name]):
        print("❌ 数据库连接信息不完整，请检查环境变量")
        return False
    
    db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    
    try:
        # 创建数据库连接
        engine = create_engine(db_url)
        
        with engine.connect() as connection:
            # 查询表结构
            result = connection.execute(text("DESCRIBE vh_sales_data"))
            columns = result.fetchall()
            
            print("=== MySQL表 vh_sales_data 结构 ===")
            print(f"{'字段名':<25} {'类型':<20} {'是否为空':<10} {'键':<10} {'默认值':<15}")
            print("-" * 80)
            
            end_time_exists = False
            end_time_info = None
            
            for column in columns:
                field, type_, null, key, default, extra = column
                print(f"{field:<25} {type_:<20} {null:<10} {key:<10} {str(default):<15}")
                
                if field == 'end_time':
                    end_time_exists = True
                    end_time_info = (field, type_, null, key, default, extra)
            
            print("-" * 80)
            
            if end_time_exists:
                print(f"✅ end_time字段存在")
                field, type_, null, key, default, extra = end_time_info
                print(f"   类型: {type_}")
                print(f"   允许NULL: {null}")
                print(f"   默认值: {default}")
                
                # 检查类型是否合适
                if 'timestamp' in type_.lower() or 'datetime' in type_.lower():
                    print("✅ 字段类型适合存储时间数据")
                else:
                    print(f"⚠️  字段类型 {type_} 可能不适合存储时间数据")
                    print("   建议类型: TIMESTAMP 或 DATETIME")
                
            else:
                print("❌ end_time字段不存在")
                print("\n建议执行以下SQL添加字段:")
                print("ALTER TABLE vh_sales_data ADD COLUMN end_time TIMESTAMP NULL COMMENT '交易结束时间';")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ 检查MySQL表结构失败: {str(e)}")
        return False


def test_insert_end_time():
    """测试插入end_time数据"""
    
    # 构建数据库连接URL
    db_host = os.getenv('DB_HOST')
    db_port = os.getenv('DB_PORT', '3306')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_name = os.getenv('DB_NAME')
    
    db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}?charset=utf8mb4"
    
    try:
        from datetime import datetime
        
        # 创建数据库连接
        engine = create_engine(db_url)
        
        with engine.connect() as connection:
            # 测试插入一条记录
            test_order_id = 'test_end_time_mysql_001'
            test_end_time = datetime.now()
            
            # 先删除可能存在的测试数据
            connection.execute(
                text("DELETE FROM vh_sales_data WHERE order_id = :order_id"),
                {"order_id": test_order_id}
            )
            
            # 插入测试数据
            insert_sql = """
            INSERT INTO vh_sales_data (
                order_id, platform_type, platform_name, main_order_id, 
                order_status, month, end_time
            ) VALUES (
                :order_id, :platform_type, :platform_name, :main_order_id,
                :order_status, :month, :end_time
            )
            """
            
            connection.execute(text(insert_sql), {
                "order_id": test_order_id,
                "platform_type": "测试平台",
                "platform_name": "测试店铺",
                "main_order_id": test_order_id,
                "order_status": "已完成",
                "month": "2025-07",
                "end_time": test_end_time
            })
            
            # 查询测试数据
            result = connection.execute(
                text("SELECT order_id, end_time FROM vh_sales_data WHERE order_id = :order_id"),
                {"order_id": test_order_id}
            )
            
            row = result.fetchone()
            if row:
                print(f"\n✅ 测试插入成功")
                print(f"   订单ID: {row[0]}")
                print(f"   交易结束时间: {row[1]}")
            else:
                print("❌ 测试插入失败：未找到插入的数据")
                return False
            
            # 清理测试数据
            connection.execute(
                text("DELETE FROM vh_sales_data WHERE order_id = :order_id"),
                {"order_id": test_order_id}
            )
            
            connection.commit()
            print("✅ 测试数据清理完成")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试插入end_time数据失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("开始检查MySQL表结构...")
    
    # 检查表结构
    structure_ok = check_mysql_table_structure()
    
    if structure_ok:
        print("\n开始测试end_time字段插入...")
        test_ok = test_insert_end_time()
        
        if test_ok:
            print("\n🎉 MySQL end_time字段检查和测试完成！")
        else:
            print("\n❌ end_time字段测试失败")
            sys.exit(1)
    else:
        print("\n❌ 表结构检查失败")
        sys.exit(1)
