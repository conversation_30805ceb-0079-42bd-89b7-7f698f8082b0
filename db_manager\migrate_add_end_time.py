#!/usr/bin/env python3
"""
数据库迁移脚本：为sales_data表添加end_time字段
"""

import sqlite3
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_folder_utils import FolderManager


def migrate_add_end_time():
    """为sales_data表添加end_time字段"""
    
    # 获取数据库文件路径
    db_path = FolderManager.get_db_file_path()
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查end_time字段是否已存在
            cursor.execute("PRAGMA table_info(sales_data)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'end_time' in columns:
                print("end_time字段已存在，无需迁移")
                return True
            
            # 添加end_time字段
            print("正在添加end_time字段...")
            cursor.execute("ALTER TABLE sales_data ADD COLUMN end_time INTEGER")
            
            # 创建索引（可选）
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_end_time ON sales_data(end_time)")
            
            conn.commit()
            print("成功添加end_time字段")
            return True
            
    except Exception as e:
        print(f"迁移失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("开始数据库迁移...")
    success = migrate_add_end_time()
    if success:
        print("数据库迁移完成")
    else:
        print("数据库迁移失败")
