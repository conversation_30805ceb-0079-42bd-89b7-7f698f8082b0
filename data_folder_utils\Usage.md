```python
from data_folder_utils import FolderManager
# 示例1：创建当前年月的文件夹
try:
    path = FolderManager.create_month_folder()
    print(f"Created current month folder at: {path}")
except Exception as e:
    print(f"Error: {e}")

# 示例2：创建指定年月的文件夹
try:
    path = FolderManager.create_month_folder(2023, 11)
    print(f"Created custom month folder at: {path}")
except Exception as e:
    print(f"Error: {e}")

# 示例3：尝试创建无效年月的文件夹（会抛出异常）
try:
    path = FolderManager.create_month_folder(2023, 13)
    print(f"Created custom month folder at: {path}")
except Exception as e:
    print(f"Error: {e}")

# 示例4：尝试只指定年份（会抛出异常）
try:
    path = FolderManager.create_month_folder(2023)
    print(f"Created custom month folder at: {path}")
except Exception as e:
    print(f"Error: {e}")
```