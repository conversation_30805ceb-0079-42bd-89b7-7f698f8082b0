from dataclasses import dataclass
from typing import Optional

@dataclass
class Order:
    order_id: str

@dataclass
class OrderInfo:
    order_id: str
    vh_code: str
    erp_code: str
    erp_id: str

@dataclass
class OrderResult:
    error_code: int
    error_msg: str
    order_id: str
    erp_system: str  # ERP系统名称（如 T+ 或 U8C）
    account_id: str  # ERP账套ID
    amount: float    # 订单金额
    bill_date: str    # 订单日期
    status: str      # 订单状态