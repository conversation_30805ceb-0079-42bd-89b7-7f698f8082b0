from enum import Enum
from typing import Optional
from datetime import datetime
import os
import pandas as pd
from data_folder_utils import FolderManager
from db_manager import OrdersManager, TableFields

class ExportPlatform(Enum):
    """支持导出的平台"""
    XIAOHONGSHU = "小红书"

class ExcelExporter:
    # Excel列名映射
    EXCEL_COLUMNS = {
        "所属月份": None,  # 新增
        "平台类型": "platform_type",
        "平台名称": "platform_name", 
        "主订单号": "main_order_id",  # 新增
        "子订单号": "order_id",  # 重命名
        "订单状态": "order_status",  # 新增订单状态列
        "商家应收金额": "merchant_receivable",
        "退款金额": "refund_amount",
        "订单金额": "order_amount",
        "应收金额": "receivable_amount",
        "发货时间": "shipping_time",  # 新增
        "U8C 029金额": "u8c_029_amount",
        "U8C 515金额": "u8c_515_amount",
        "T+002金额": "t_plus_002_amount",
        "T+008金额": "t_plus_008_amount",
        "销货单合计": "sales_order_total",
        "销货单日期": "sales_order_date",
        "本月未发货金额": "unshipped_amount",
        "已回款金额": "received_amount",
        "回款日期": "payment_date",
        "收款渠道": "payment_channel",
        "待回款": "pending_payment",
        "备注": None  # 空列
    }

    def __init__(self):
        """初始化导出器"""
        self.db_path = FolderManager.get_db_file_path()
        self.orders_manager = OrdersManager(self.db_path)

    def export_monthly_summary(
        self,
        year: int, 
        month: int, 
        platform: ExportPlatform = ExportPlatform.XIAOHONGSHU,
        output_path: Optional[str] = None
    ) -> str:
        """导出月度订单明细Excel"""
        # 验证输入
        if not (1 <= month <= 12):
            raise ValueError(f"Invalid month: {month}")
        if year < 2000:
            raise ValueError(f"Invalid year: {year}")

        # 确定输出路径
        if output_path is None:
            output_path = os.getcwd()
        os.makedirs(output_path, exist_ok=True)

        # 生成文件名
        filename = f"{platform.value}订单明细_{year:04d}-{month:02d}.xlsx"
        full_path = os.path.join(output_path, filename)

        # 使用SQL查询来获取指定平台的所有数据
        query = """
        SELECT * FROM sales_data 
        WHERE platform_type = :platform_type
        ORDER BY platform_name, payment_date
        """
        params = {
            'platform_type': platform.value
        }

        # 使用OrdersManager的execute_query方法
        result = self.orders_manager.execute_query(query, params)
        
        if not result:
            print(f"没有找到 {year}年{month}月 的{platform.value}订单数据")
            return None
        
        # 转换查询结果为DataFrame
        columns = [
            'order_id', 'platform_type', 'platform_name', 'main_order_id',
            'order_status', 'shipping_time',
            'merchant_receivable', 'refund_amount', 'order_amount', 'receivable_amount',
            'u8c_029_amount', 'u8c_515_amount', 't_plus_002_amount', 't_plus_008_amount',
            'sales_order_total', 'sales_order_date', 'unshipped_amount', 'received_amount', 'payment_date',
            'payment_channel', 'pending_payment'
        ]
        df = pd.DataFrame(result, columns=columns)

        # 如果没有数据，返回None
        if df.empty:
            print(f"没有找到 {year}年{month}月 的{platform.value}订单数据")
            return None

        # 格式化数据
        df = self._format_dataframe(df, year, month)

        # 保存到Excel
        self._save_to_excel(df, full_path)
        
        print(f"已导出订单明细到: {full_path}")
        return full_path

    def _format_dataframe(self, df: pd.DataFrame, year: int, month: int) -> pd.DataFrame:
        """格式化DataFrame"""
        # 添加所属月份列
        df['所属月份'] = f"{year}年{month}月"
        
        # 转换时间戳为日期格式
        df['shipping_time'] = pd.to_datetime(df['shipping_time'], unit='s').dt.strftime('%Y-%m-%d')
        
        # 重命名列
        reverse_columns = {v: k for k, v in self.EXCEL_COLUMNS.items() if v is not None}
        df = df.rename(columns=reverse_columns)
        
        # 添加空的备注列和其他可能缺失的列
        for col in self.EXCEL_COLUMNS.keys():
            if col not in df.columns:
                df[col] = ''  # 对于缺失的列，添加空值
        
        # 格式化金额列(保留2位小数)
        money_columns = [
            '商家应收金额', '退款金额', '订单金额', '应收金额',
            'U8C 029金额', 'U8C 515金额', 'T+002金额', 'T+008金额',
            '销货单合计', '销货单日期', '本月未发货金额', '已回款金额', '待回款'
        ]
        
        for col in money_columns:
            if col in df.columns:
                df[col] = df[col].round(2)

        # 确保列顺序与EXCEL_COLUMNS一致
        df = df[list(self.EXCEL_COLUMNS.keys())]
        
        return df

    def _save_to_excel(self, df: pd.DataFrame, filepath: str):
        """保存DataFrame到Excel文件"""
        # 创建Excel写入器
        writer = pd.ExcelWriter(filepath, engine='xlsxwriter')
        
        try:
            # 写入数据
            df.to_excel(writer, index=False, sheet_name='订单明细')
            
            # 获取workbook和worksheet对象
            workbook = writer.book
            worksheet = writer.sheets['订单明细']
            
            # 设置列宽
            for idx, col in enumerate(df.columns):
                max_length = max(
                    df[col].astype(str).apply(len).max(),
                    len(col)
                )
                worksheet.set_column(idx, idx, max_length + 2)
            
        finally:
            # 保存文件
            writer.close()

def export_monthly_summary(
    year: int, 
    month: int, 
    platform: ExportPlatform = ExportPlatform.XIAOHONGSHU,
    output_path: Optional[str] = None
) -> str:
    """
    导出指定平台的月度订单汇总Excel
    
    Args:
        year: 年份
        month: 月份
        platform: 平台类型,默认为小红书
        output_path: 输出路径,可选,默认为当前目录
    
    Returns:
        str: 生成的Excel文件完整路径
    """
    exporter = ExcelExporter()
    return exporter.export_monthly_summary(year, month, platform, output_path)

if __name__ == "__main__":
    # 示例：导出2024年3月的小红书订单汇总
    export_monthly_summary(2024, 2)