from datetime import datetime, timedelta
from typing import Dict, Any, List

import shop_manager
from db_manager import TableFields
from db_manager.base_db_manager import BaseDBManager
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop

logger = logging.getLogger(__name__)


class Taobao(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.TAOBAO.value

    def run(self, shop: Shop, db: BaseDBManager, start_date: datetime, end_date: datetime) -> list[
        Dict[TableFields, Any]]:

        query = """
        SELECT COUNT(*) FROM settlements 
        WHERE platform_id = ? 
        AND settlement_time >= ? 
        AND settlement_time <= ?
        """
        result = db.execute_query(
            query, (shop.shop_id, start_date, end_date))
        if result and result[0][0] > 0:
            logger.info(
                f"店铺 {shop.shop_name} 的 {start_date.strftime('%Y-%m')} 月数据已存在，跳过处理")
            return []

        # 拿到店铺key
        shop_info = shop.get_shop_info()
        if shop_info is None:
            raise PlatformRunError(f"店铺信息获取失败")

        # 处理淘宝API的30天时间窗口限制
        all_data = []
        current_start = start_date
        
        # 分割时间窗口，每个窗口不超过30天
        while current_start < end_date:
            # 计算当前窗口的结束时间，不超过原始结束时间且与起始时间相差不超过30天
            window_end = min(end_date, current_start + timedelta(days=29, hours=23, minutes=59, seconds=59))
            
            logger.info(f"处理时间窗口: {current_start.strftime('%Y-%m-%d')} 至 {window_end.strftime('%Y-%m-%d')}")
            
            # 调用API获取该时间窗口的数据
            window_data = self._fetch_settlement_data(shop, shop_info, current_start, window_end)
            all_data.extend(window_data)
            
            # 移动到下一个窗口的起始时间
            current_start = window_end + timedelta(seconds=1)

        # 处理并保存所有获取的数据
        data = []
        for row in all_data:
            amount = float(row['total_amount']) / 100
            settlement_type = "收入"
            if amount < 0:
                settlement_type = "退款"

            data.append({
                TableFields.ORDER_ID: str(row['order_id']),
                TableFields.PLATFORM_ID: shop.shop_id,
                TableFields.PLATFORM_NAME: shop.shop_name,
                TableFields.PLATFORM_TYPE: self.platform_type(),
                TableFields.SETTLEMENT_TIME: row['book_time'],
                TableFields.SETTLEMENT_AMOUNT: amount,
                TableFields.SETTLEMENT_TYPE: settlement_type,
                TableFields.SETTLEMENT_CHANNEL: "",
            })

        if len(data) > 0:
            logger.info(f"获取到 {len(data)} 条结算记录，准备保存到数据库")
            if not db.insert_batch_data(data):
                raise PlatformRunError(f"保存数据失败")
        else:
            logger.info(f"未获取到任何结算记录")

        return data
    
    def _fetch_settlement_data(self, shop: Shop, shop_info: dict, start_time: datetime, end_time: datetime) -> List[Dict]:
        """
        获取指定时间范围内的结算数据
        
        Args:
            shop: 店铺信息
            shop_info: 店铺配置信息
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            结算数据列表
        """
        # 请求接口拿到下载地址
        url = 'http://39.101.137.50/taobao/bill_details'
        post_data = {
            'sessionKey': shop_info['access_token'],
            'startTime': start_time.strftime("%Y-%m-%d %H:%M:%S"),
            'endTime': end_time.strftime("%Y-%m-%d %H:%M:%S"),
        }
        
        logger.info(f"请求淘宝结算数据 {post_data['startTime']} 至 {post_data['endTime']}")
        
        try:
            resp = requests.post(url, post_data).json()
            
            if resp.get('errorCode', -1) != "0":
                error_msg = resp.get('errorMsg', '未知错误')
                logger.error(f"获取结算数据失败: {error_msg}")
                raise PlatformRunError(f"获取结算数据失败: {error_msg}")
            
            data = resp.get('data', [])
            logger.info(f"成功获取 {len(data)} 条结算记录")
            return data
            
        except Exception as e:
            logger.error(f"请求淘宝结算数据异常: {str(e)}")
            raise PlatformRunError(f"请求淘宝结算数据异常: {str(e)}")
