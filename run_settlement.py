from data_folder_utils import FolderManager
from db_manager import OrdersManager, SettlementManager, TableFields

from datetime import datetime
import logging
from settlement_downloader.downloader import SettlementDownloader
from settlement_downloader.platforms.xiaohongshu import Xiaohongshu
from settlement_downloader.platforms.taobao import Taobao
from settlement_downloader.platforms.douyin import Douyin
from settlement_downloader.platforms.dewu import DeWu

# 配置日志

logging.basicConfig(level=logging.INFO)

# 初始化数据库管理器
db_path = FolderManager.get_db_file_path()
db_settlement = SettlementManager(db_path)

# 创建下载器实例
downloader = SettlementDownloader(db_settlement, platforms=[
    # Xiaohongshu()
    Taobao()
    # Douyin()
    # DeWu()
])

# 定义时间范围
start_date = datetime(2025, 3, 1)
end_date = datetime(2025, 3, 30, 23, 59, 59)

# 执行下载
downloader.download_settlements(
    start_date=start_date,
    end_date=end_date,
    max_attempts=5  # 最多尝试5次
)
