from typing import List, Dict
from .models import Order, OrderResult, OrderInfo
from .erp_system import ERPSystem, TPlusSystem, U8CSystem

class OrderQuery:
    def __init__(self):
        # 初始化ERP系统实例
        self.erp_systems: Dict[str, ERPSystem] = {
            "platform_tplus": TPlusSystem(),
            "platform_u8c": U8CSystem(),
        }

    def query_single_order(self, order: Order) -> OrderResult:
        """查询单个订单"""
        info = OrderInfo(
            order_id=order.order_id,
            vh_code='',
            erp_code='',
            erp_id='',
        )

        result = ERPSystem.convert_order_to_order_result(info)

        corp_mapping = ERPSystem.get_corp_by_order_no([order.order_id])
        vh_code = corp_mapping.get(order.order_id, '')
        if not vh_code:
            result.error_code = -1
            result.error_msg = "订单号不存在"
            return result

        code_map = ERPSystem.get_corp_mapping_by_vh_code()

        code_data = code_map.get(vh_code, {})
        info.vh_code = vh_code
        info.erp_code = str(code_data.get('erp_code', ''))
        info.erp_id = str(code_data.get('erp_id', ''))

        erp_system = self._get_erp_system(info.erp_id)
        if not erp_system:
            result.error_code = -1
            result.error_msg = f"不支持的平台: {info.erp_id}"
            return result
        return erp_system.query_order(info)

    def query_batch_orders(self, orders: List[Order]) -> List[OrderResult]:
        """批量查询订单"""
        order_list = self.convert_orders_to_order_info(orders)

        order_nos = [order.order_id for order in order_list]
        corp_mapping = ERPSystem.get_corp_by_order_no(order_nos)
        code_map = ERPSystem.get_corp_mapping_by_vh_code()

        for order in order_list:
            vh_code = corp_mapping.get(order.order_id, '')
            code_data = code_map.get(vh_code, {})
            order.vh_code = vh_code
            order.erp_code = str(code_data.get('erp_code', ''))
            order.erp_id = str(code_data.get('erp_id', ''))

        # 按平台分组订单
        orders_by_platform = self._group_orders_by_platform(order_list)
        
        results = []
        for platform, platform_orders in orders_by_platform.items():
            erp_system = self._get_erp_system(platform)
            if not erp_system:
                erp_system = U8CSystem()
            results.extend(erp_system.query_orders(platform_orders))
        
        return results

    def _get_erp_system(self, platform: str) -> ERPSystem:
        """根据平台获取对应的ERP系统"""
        platform_to_erp = {
            "2": "platform_tplus",
            "1": "platform_u8c",
        }
        erp_key = platform_to_erp.get(platform)
        return self.erp_systems.get(erp_key) if erp_key else None

    def _group_orders_by_platform(self, orders: List[OrderInfo]) -> Dict[str, List[OrderInfo]]:
        """按平台分组订单"""
        grouped = {}
        for order in orders:
            if order.erp_id not in grouped:
                grouped[order.erp_id] = []
            grouped[order.erp_id].append(order)
        return grouped
    
    def convert_orders_to_order_info(self, orders: List[Order]) -> List[OrderInfo]:
        """将 Order 转为 OrderInfo"""
        order_info_list = []
        for order in orders:
            order_info = OrderInfo(
                order_id=order.order_id,
                vh_code="",
                erp_code="",
                erp_id=""
            )
            order_info_list.append(order_info)
        return order_info_list