@startuml shop

' 枚举类型
enum Entity {
  TECH = "科技"
  CLOUD_WINE = "云酒"
  MULTI_ENTITY = "多主体"
  RABBIT_PLANET = "兔子星球"
}

enum PlatformType {
  DOUYIN = "抖音"
  JD = "京东"
  PINDUODUO = "拼多多"
  OTHER = "其他平台"
  TAOBAO = "淘系"
  KUAI_TUAN = "快团团"
  WECHAT = "微信小程序"
  XIAOHONGSHU = "小红书"
}

enum PlatformName {
  ' 抖音平台
  DOUYIN_JIUYUN_FLAGSHIP = "酒云网抖店-卖场旗舰店"
  ' 京东平台
  JD_BAINIANG_SELF = "佰酿科技（京东自营）"
  JD_YUNWINE = "云酒京东店铺"
  JD_FRANCE_SOUTH = "法国南部葡萄酒官方产区馆"
  ' 拼多多平台
  PDD_BAINIANG_WINE = "佰酿云酒酒类专营店（拼多多店）"
  ' 得物平台
  DEWU_BAINIANG = "佰酿科技（得物APP）"
  ' 东方甄选平台
  DONGFANG_APP = "东方甄选APP"
  ' 淘宝/天猫平台
  TB_BAINIANG_WINE = "佰酿美酒天猫"
  TB_BAINIANG_FLAG = "佰酿科技（天猫旗舰店）"
  TB_GLOBAL_TECH = "天猫国际(科技)"
  TB_TAOGONGZI = "桃公子淘宝店"
  TB_FRANCE_SOUTH = "天猫（法国南部葡萄酒官方旗舰店）"
  TB_SUPER = "天猫超市"
  TB_JIUYUMIAO = "酒遇喵掌柜"
  TB_MEINIDUO = "美尼多天猫旗舰店"
  TB_YUNWINE_GLOBAL = "佰酿云酒（天猫国际）"
  ' 快团团平台
  KTT_YANGSHU = "杨树禄-快团团"
  KTT_RABBIT = "快团团-兔子福利社"
  ' 其他平台
  OTHER_LAOWAI = "老外买酒"
  ' 微信视频号
  WECHAT_JIUYUN = "酒云网视频号"
  ' 小红书平台
  XHS_YUNWINE = "行吟信息科技（武汉）有限公司(云酒网小红书店）"
  XHS_RABBIT = "兔子小红书"
  XHS_BROWN_BROTHERS = "小红书-Brown Brothers布琅兄弟"
  XHS_WEI_DISTILLERY = "小红书-威哥蒸馏所"
}

' ShopInfo 类
class ShopInfo {
  -entity: Entity
  -platform_type: PlatformType
  -platform_name: PlatformName
  -backend_name: str
  -payment_accounts: List[str]
  -bank_subjects: List[str]
  +__init__(entity: Entity, platform_type: PlatformType, platform_name: PlatformName, backend_name: str, payment_accounts: List[str], bank_subjects: List[str])
  +__str__(): str
}

' ShopManager 类（单例模式）
class ShopManager {
  -_instance: ShopManager
  -_default_config_path: str = "shop_manager/config.json"
  -shops: Dict[str, ShopInfo]
  +__new__(config_path: str = None): ShopManager
  -_initialize(config_path: str)
  -load_from_config(config_path: str)
  +add_shop(shop: ShopInfo)
  +get_by_platform_name(platform_name: PlatformName): List[Dict[str, List[str]]]
  +get_all_shops(): List[ShopInfo]
}

' 依赖和关联关系
ShopInfo o--> "1" Entity
ShopInfo o--> "1" PlatformType
ShopInfo o--> "1" PlatformName

ShopManager o--> "0..*" ShopInfo : manages

note right of ShopManager
  单例模式实现：
  - 通过 __new__ 确保只有一个实例
  - 默认配置文件路径为 "shop_manager/config.json"
end note

note right of ShopInfo
  - payment_accounts 和 bank_subjects 长度必须一致
  - 若不一致则抛出 ValueError
end note

@enduml