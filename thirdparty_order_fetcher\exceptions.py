class PlatformRunError(Exception):
    """平台运行异常基类"""
    def __init__(self, message: str = "平台操作异常"):
        self.message = message
        super().__init__(self.message)

class APIConnectionError(PlatformRunError):
    """API连接异常"""
    def __init__(self, message: str = "平台API连接失败"):
        super().__init__(message)

class AuthFailedError(PlatformRunError):
    """认证失败异常"""
    def __init__(self, message: str = "平台认证失败"):
        super().__init__(message)

class OrderFetchError(PlatformRunError):
    """订单获取异常"""
    def __init__(self, message: str = "订单数据获取失败"):
        super().__init__(message) 