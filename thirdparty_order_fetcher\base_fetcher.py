# thirdparty_order_fetcher/base_fetcher.py
from abc import ABC, abstractmethod
from datetime import datetime
from typing import List
from shop_manager.constants import PlatformName, PlatformType
from .models import Order

class BaseOrderFetcher(ABC):
    def __init__(self, platform_name: PlatformName, platform_type: PlatformType):
        self.platform_name = platform_name
        self.platform_type = platform_type
    
    @abstractmethod
    def fetch_orders(self, year: int, month: int) -> List[Order]:
        """
        Fetch orders for specified year and month
        
        Args:
            year: Target year (e.g., 2025)
            month: Target month (1-12)
        
        Returns:
            List of Order objects
        """
        pass
    
    def validate_date(self, year: int, month: int) -> None:
        """Validate year and month parameters"""
        if not 1 <= month <= 12:
            raise ValueError("Month must be between 1 and 12")
        if year < 2000 or year > datetime.now().year:
            raise ValueError(f"Year must be between 2000 and {datetime.now().year}")