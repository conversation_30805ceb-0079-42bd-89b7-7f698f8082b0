## 项目说明

本项目负责生成三方订单看板数据。

## 项目结构说明

```
├── data_folder_utils 用于根据日期创建数据存储文件夹
│   ├── __init__.py
│   ├── folder_manager.py 用于根据日期创建数据存储文件夹
├── erp_order_query 用于查询ERP系统中的订单数据
│   ├── __init__.py
│   ├── config.py 配置文件
│   ├── erp_system.py 实现查询ERP系统中的订单数据核心文件
│   ├── models.py 订单数据模型
│   ├── order_query.py 用于查询ERP系统中的订单数据
├── index.py 主程序入口
├── README.md
├── requirements.txt
├── db_manager Sqlite数据库管理
│   ├── __init__.py
│   ├── db_manager.py 数据库管理核心文件
│   ├── table_fields.py 数据库字段枚举
│   ├── templates 模板文件
│   │   └── db.html 数据库管理Web服务页面
│   └── web_server.py 数据库管理Web服务接口
├── shop_manager
│   ├── __init__.py
│   ├── config.json 店铺相关配置数据
│   ├── constants.py 店铺相关常量
│   └── shop.py 店铺管理核心文件
├── settlement_downloader 用于下载结算数据
│   ├── __init__.py
│   ├── base.py
│   ├── downloader.py
│   ├── exceptions.py
│   ├── platforms
│   │   ├── platform_b.py 平台B的下载器
│   │   └── platforms_a.py 平台A的下载器
│   └── utils.py
├── thirdparty_order_fetcher 三方平台订单获取
│   ├── __init__.py
│   ├── base_fetcher.py 三方平台订单获取基类
│   ├── fetchers 三方平台订单获取具体实现
│   │   ├── _init__.py
│   │   ├── jd_fetcher.py 京东订单获取
│   │   └── taobao_fetcher.py 淘宝订单获取
│   └── models.py 三方平台订单数据模型
└── vinehoo_order_fetcher 中台三方订单获取
    ├── __init__.py
    ├── exceptions.py 订单获取异常
    ├── models.py 中台三方订单数据模型
    ├── order_fetcher.py 中台三方订单获取核心文件
```

## 时序图
```mermaid
sequenceDiagram
    participant S as 统计脚本
    participant M as 中台
    participant DB as SQLite数据库
    participant P as 三方平台
    participant E as ERP系统
    
    S->>M: 获取指定月份所有三方订单数据
    M-->>S: 返回订单数据
    S->>DB: 保存到订单汇总表
    
    S->>M: 获取指定月份三方店铺订单数据
    M-->>S: 返回店铺订单数据
    S->>DB: 保存到订单汇总表(以店铺状态为准)
    
    S->>P: 下载指定月份结算订单
    P-->>S: 返回结算订单数据
    S->>DB: 保存到结算表
    
    loop 遍历结算表每条记录
        S->>DB: 获取结算表订单入账金额
        alt 订单汇总表存在该订单
            S->>DB: 更新订单汇总表对应订单数据
        else 订单汇总表不存在
            S->>M: 获取该订单数据
            M-->>S: 返回订单数据
            S->>DB: 保存到订单汇总表并更新数据
        end
    end
    
    loop 遍历订单汇总表
        S->>E: 获取对应订单销货单金额
        E-->>S: 返回销货单金额
        S->>DB: 更新订单汇总表订单数据
    end
```