import sqlite3
import os
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
from .table_fields import TableFields

class BaseDBManager(ABC):
    """数据库管理基类"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        self._initialize_database()

    @abstractmethod
    def _initialize_database(self):
        """创建数据库和表的抽象方法，子类必须实现"""
        pass

    def _normalize_keys(self, data: Dict[Any, Any], valid_fields: set) -> Dict[str, Any]:
        """
        将键转换为字符串类型的键
        
        Args:
            data (Dict[Any, Any]): 需要规范化的数据字典
            valid_fields (set): 有效字段名集合
            
        Returns:
            Dict[str, Any]: 规范化后的数据字典
        """
        normalized_data = {}
        
        for key, value in data.items():
            if hasattr(key, 'value'):  # 处理枚举类型
                normalized_key = str(key.value)
            elif isinstance(key, str):
                normalized_key = key
            else:
                raise TypeError(f"Invalid key type: {type(key)}. Must be Enum or str.")
                
            if normalized_key in valid_fields:
                normalized_data[normalized_key] = value
            else:
                raise ValueError(f"Invalid field name: {normalized_key}. Must be one of {valid_fields}")
                
        return normalized_data

    def order_exists(self, order_id: str) -> bool:
        """检查指定的订单ID是否存在于数据库中"""
        pass

    def insert_data(self, data: Dict[TableFields, Any]) -> bool:
        """插入数据"""
        pass

    def insert_batch_data(self, data: List[Dict[TableFields, Any]]) -> bool:
        """批量插入数据"""
        pass

    def update_data(self, order_id: str, data: Dict[TableFields, Any]) -> bool:
        """更新数据"""
        pass

    def execute_query(self, query: str, params: tuple = ()) -> Any:
        """
        执行SQL查询
        
        Args:
            query (str): SQL查询语句
            params (tuple): 查询参数
            
        Returns:
            Any: 查询结果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.fetchall()
        except Exception as e:
            print(f"Query execution error: {e}")
            return None

    def execute_query_one(self, query: str, params: tuple = ()) -> Any:
        """
        执行SQL查询并返回单条结果
        
        Args:
            query (str): SQL查询语句
            params (tuple): 查询参数
            
        Returns:
            Any: 查询结果的第一条记录
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.fetchone()
        except Exception as e:
            print(f"Query execution error: {e}")
            return None

    def paginate_query(self, base_query: str, page: int = 1, page_size: int = 10,
                      where_clause: str = '', params: List[Any] = None) -> Dict[str, Any]:
        """
        分页查询
        
        Args:
            base_query (str): 基础查询语句
            page (int): 页码
            page_size (int): 每页数量
            where_clause (str): WHERE子句
            params (List[Any]): 查询参数
            
        Returns:
            Dict[str, Any]: 分页查询结果
        """
        try:
            params = params or []
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询总数
                count_query = f'SELECT COUNT(*) FROM ({base_query}){where_clause}'
                cursor.execute(count_query, params)
                total = cursor.fetchone()[0]
                
                # 分页查询
                offset = (page - 1) * page_size
                query = f'{base_query}{where_clause} LIMIT ? OFFSET ?'
                params.extend([page_size, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'data': [dict(zip(columns, row)) for row in rows]
                }
        except Exception as e:
            print(f"Pagination query error: {e}")
            return {'error': str(e)} 