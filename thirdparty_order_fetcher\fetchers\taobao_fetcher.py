# thirdparty_order_fetcher/fetchers/taobao_fetcher.py
import hashlib
import json
import os
import time
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Any

import requests

from data_folder_utils import FolderManager
from db_manager import OrdersManager
from db_manager.table_fields import TableFields
from db_manager.order_status import OrderStatus
from shop_manager.constants import PlatformName, PlatformType
from settlement_downloader.platform_shop_config import PlatformShopConfig
from ..base_fetcher import BaseOrderFetcher
from ..exceptions import PlatformRunError, APIConnectionError
from ..models import Order
from erp_order_query import OrderQuery, Order as ERPOrder


class TaobaoOrderFetcher(BaseOrderFetcher):
    # API基础URL
    API_URL = "https://eco.taobao.com/router/rest"
    
    # 淘宝API版本
    API_VERSION = "2.0"
    
    # 订单状态映射
    ORDER_STATUS_MAP = {
        "WAIT_BUYER_PAY": "PENDING",             # 等待买家付款
        "TRADE_NO_CREATE_PAY": "PENDING",         # 没有创建支付宝交易
        "WAIT_SELLER_SEND_GOODS": "PROCESSING",   # 等待卖家发货,即:买家已付款
        "SELLER_CONSIGNED_PART": "SHIPPED",       # 卖家部分发货
        "WAIT_BUYER_CONFIRM_GOODS": "SHIPPED",    # 等待买家确认收货,即:卖家已发货
        "TRADE_BUYER_SIGNED": "COMPLETED",        # 买家已签收,货到付款专用
        "TRADE_FINISHED": "COMPLETED",            # 交易成功
        "TRADE_CLOSED": "CLOSED",                 # 付款以后用户退款成功，交易自动关闭
        "TRADE_CLOSED_BY_TAOBAO": "CANCELLED",    # 付款以前，卖家或买家主动关闭交易
        "PAY_PENDING": "PENDING",                 # 国际信用卡支付付款确认中
        "PAID_FORBID_CONSIGN": "PROCESSING"       # 已付款但禁止发货
    }

    # 淘宝应用凭证配置
    TAOBAO_APP_CONFIGS = {
        # 第一个应用凭证
        'app1': {
            'app_key': '32323720',
            'app_secret': '450dd2a5a9a7d92b15a2c2c40d92e079'
        }
    }
    
    # 店铺与应用的映射关系
    SHOP_APP_MAPPING = {
        # '店铺ID': '应用配置键名'
        '227734657': 'app1',  # 佰酿美酒天猫
        '419938814': 'app1',  # 佰酿科技（天猫旗舰店）
        '566768114': 'app1',  # 桃公子淘宝店
        '558695549': 'app1',  # 天猫（法国南部葡萄酒官方旗舰店）
        '452890329': 'app1',  # 酒遇喵掌柜
        '541276454': 'app1',  # 美尼多天猫旗舰店
    }
    
    def __init__(self):
        super().__init__(
            platform_name=PlatformName.TB_BAINIANG_WINE,
            platform_type=PlatformType.TAOBAO
        )
        # 从PlatformShopConfig获取淘宝店铺配置
        self.platform_shop_config = PlatformShopConfig()
        self.shops = self.platform_shop_config.get_shops_by_platform(PlatformType.TAOBAO.value)
        
        # 初始化订单管理器，用于保存订单到本地数据库
        self.orders_manager = OrdersManager(FolderManager.get_db_file_path())
        
        # 初始化所有店铺信息
        self.shops_info = self._get_all_shops_info()
        self.order_query = OrderQuery()  # 初始化ERP订单查询器
        
        # 设置API请求重试参数
        self.max_retries = 3
        self.retry_delay = 2  # 秒

        # 确保订单处理记录表存在
        self._ensure_order_process_table_exists()

        print(f"从配置中获取到 {len(self.shops_info)} 个淘宝店铺")
    
    def _ensure_order_process_table_exists(self):
        """确保订单处理记录表存在"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS order_process_records (
            order_id TEXT PRIMARY KEY,
            process_status INTEGER DEFAULT 0,  -- 0=待处理, 1=成功
            process_date TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        try:
            self.orders_manager.execute_query(create_table_sql)
            # 获取当前日期
            today_date = date.today().strftime('%Y-%m-%d')
            
            # 删除非当日的订单处理记录
            delete_old_records_sql = """
            DELETE FROM order_process_records
            WHERE process_date != ?
            """
            
            try:
                result = self.orders_manager.execute_query(delete_old_records_sql, (today_date,))
                print(f"已清理非当日({today_date})的历史订单处理记录")
            except Exception as e:
                print(f"清理历史订单处理记录失败: {str(e)}")
                
            print("订单处理记录表初始化成功")
        except Exception as e:
            print(f"初始化订单处理记录表失败: {str(e)}")
            raise
    
    def _save_order_ids_to_process_table(self, order_ids: set, today_date: str):
        """将订单ID保存到处理表中"""
        if not order_ids:
            return
        
        try:
            # 查询已存在的订单记录
            existing_order_ids = set()
            for order_id in order_ids:
                query = "SELECT order_id FROM order_process_records WHERE order_id = ?"
                result = self.orders_manager.execute_query_one(query, (order_id,))
                if result:
                    existing_order_ids.add(result[0])
            
            # 需要插入的新订单ID
            new_order_ids = order_ids - existing_order_ids
            
            # 插入新订单记录
            if new_order_ids:
                insert_query = """
                INSERT INTO order_process_records (order_id, process_status, process_date)
                VALUES (?, 0, ?)
                """
                
                for order_id in new_order_ids:
                    try:
                        self.orders_manager.execute_query(insert_query, (order_id, today_date))
                    except Exception as e:
                        print(f"插入订单处理记录失败 ({order_id}): {str(e)}")
                
                print(f"成功添加 {len(new_order_ids)} 个新订单到处理表")
            else:
                print("没有新订单需要添加到处理表")
                
        except Exception as e:
            print(f"保存订单ID到处理表失败: {str(e)}")
    
    def _get_pending_order_ids(self, order_ids: set, today_date: str) -> set:
        """获取今日待处理的订单ID"""
        if not order_ids:
            return set()
        
        pending_order_ids = set()
        
        try:
            for order_id in order_ids:
                query = """
                SELECT order_id FROM order_process_records 
                WHERE order_id = ? AND process_date = ? AND process_status = 0
                """
                result = self.orders_manager.execute_query_one(query, (order_id, today_date))
                if result:
                    pending_order_ids.add(result[0])
            
            print(f"找到 {len(pending_order_ids)} 个待处理订单")
            return pending_order_ids
        except Exception as e:
            print(f"获取待处理订单ID失败: {str(e)}")
            return set()
    
    def _mark_order_as_processed(self, order_id: str):
        """将订单标记为已处理"""
        update_query = """
        UPDATE order_process_records
        SET process_status = 1
        WHERE order_id = ?
        """
        
        try:
            self.orders_manager.execute_query(update_query, (order_id,))
            print(f"订单 {order_id} 已标记为处理成功")
        except Exception as e:
            print(f"标记订单为已处理失败 ({order_id}): {str(e)}")
    
    def _check_pending_orders(self, year: int, month: int) -> bool:
        """
        检查当日是否还有待处理的订单
        
        Args:
            year: 年份
            month: 月份
            
        Returns:
            是否有待处理订单
        """
        today_date = date.today().strftime('%Y-%m-%d')
        
        # 检查订单处理表中是否有待处理的订单
        query = """
        SELECT COUNT(*) FROM order_process_records 
        WHERE process_date = ? AND process_status = 0
        """
        
        try:
            count = self.orders_manager.execute_query_one(query, (today_date,))
            pending_count = count[0] if count else 0
            
            print(f"当日还有 {pending_count} 个订单待处理")
            
            return pending_count > 0
            
        except Exception as e:
            print(f"检查待处理订单失败: {str(e)}")
            return False
    
    def _get_all_shops_info(self) -> Dict[str, dict]:
        """获取所有淘宝店铺信息"""
        shops_info = {}
        
        print(f"正在获取 {len(self.shops)} 个淘宝店铺信息...")
        
        for shop in self.shops:
            shop_id = shop.shop_id
            shop_name = shop.shop_name
            
            try:
                # 使用shop对象的get_shop_info方法获取店铺信息（包含access_token）
                shop_info = shop.get_shop_info()
                if shop_info is None:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 信息获取失败，将跳过")
                    continue
                
                # 确定该店铺使用哪个应用配置
                app_key = self.SHOP_APP_MAPPING.get(shop_id, 'app1')  # 默认使用app1
                app_config = self.TAOBAO_APP_CONFIGS.get(app_key)
                
                if not app_config:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 没有对应的应用配置，将跳过")
                    continue
                
                # 动态添加API调用所需的属性
                shop.app_key = app_config['app_key']
                shop.app_secret = app_config['app_secret']
                
                # 从shop_info中获取access_token
                if 'access_token' in shop_info:
                    shop.access_token = shop_info['access_token']
                else:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 缺少access_token，将跳过")
                    continue
                
                shops_info[shop_id] = shop_info
                print(f"成功获取店铺信息: {shop_name}({shop_id})，使用应用配置: {app_key}")
            except Exception as e:
                print(f"错误: 获取店铺 {shop_name}({shop_id}) 信息时出错: {str(e)}")
        
        if not shops_info:
            raise PlatformRunError("所有淘宝店铺信息获取失败")
            
        return shops_info
    
    def fetch_orders(self, year: int, month: int) -> List[Order]:
        """获取指定年月的淘宝订单"""
        self.validate_date(year, month)
        
        if not self.shops_info:
            raise PlatformRunError("淘宝店铺信息未初始化")
        
        all_orders = []
        max_fetch_iterations = 5  # 最大重试次数，避免无限循环
        
        for iteration in range(max_fetch_iterations):
            if iteration > 0:
                print(f"第 {iteration+1} 次尝试获取待处理订单...")
            
            current_iteration_orders = []
            
            # 对每个店铺获取订单
            for shop in self.shops:
                try:
                    # 检查是否有API凭证
                    if not hasattr(shop, 'app_key') or not hasattr(shop, 'access_token'):
                        print(f"跳过店铺 {shop.shop_name}，没有配置API凭证")
                        continue
                    
                    shop_orders = self._fetch_orders_for_month(shop, year, month)
                    current_iteration_orders.extend(shop_orders)
                    print(f"从淘宝店铺 {shop.shop_name} 获取到 {len(shop_orders)} 个订单")
                except Exception as e:
                    print(f"获取淘宝店铺 {shop.shop_name} 订单失败: {str(e)}")
            
            all_orders.extend(current_iteration_orders)
            
            # 检查是否还有待处理的订单
            has_pending_orders = self._check_pending_orders(year, month)
            
            if not has_pending_orders or not current_iteration_orders:
                # 如果没有待处理订单或者本次没有获取到订单，就退出循环
                break
                
            # 等待一段时间后继续下一轮获取
            if iteration < max_fetch_iterations - 1 and has_pending_orders:
                wait_time = 5  # 等待5秒
                print(f"还有订单待处理，{wait_time}秒后将进行下一轮获取...")
                time.sleep(wait_time)
        
        total_processed = len(all_orders)
        print(f"所有轮次共获取处理了 {total_processed} 个订单")
        
        # 再次检查是否还有未处理的订单
        if self._check_pending_orders(year, month):
            print("警告: 仍有部分订单未能成功处理，可以稍后再次运行来尝试处理这些订单")
        else:
            print("所有订单已成功处理")
            
        return all_orders
    
    def get_order_detail(self, shop, order_id: str) -> Optional[Dict[str, Any]]:
        """
        获取单个订单的详细信息
        
        Args:
            shop: 店铺信息
            order_id: 订单ID
            
        Returns:
            订单详细信息字典，如果获取失败则返回None
        """
        try:
            # 调用淘宝API获取订单详情，使用重试机制
            result = self._call_taobao_api_with_retry(
                shop=shop,
                method="taobao.trade.fullinfo.get",
                params={
                    "tid": order_id,
                    "fields": "tid,payment_method,type,status,payment,buyer_message,seller_memo,created,pay_time,consign_time,end_time,receiver_name,receiver_address,receiver_mobile,receiver_phone,orders,promotion_details,received_payment"
                }
            )

            # 解析API返回结果
            if "trade_fullinfo_get_response" in result and "trade" in result["trade_fullinfo_get_response"]:
                return result["trade_fullinfo_get_response"]["trade"]
            else:
                print(f"获取淘宝订单 {order_id} 详情失败: API返回数据格式错误")
                return None
                
        except Exception as e:
            print(f"获取淘宝订单 {order_id} 详情失败: {str(e)}")
            return None
    
    def _extract_sub_orders(self, order_detail: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从订单详情中提取子订单信息
        
        Args:
            order_detail: 订单详情数据
            
        Returns:
            子订单列表
        """
        sub_orders = []
        
        # 检查是否有子订单
        if "orders" in order_detail and "order" in order_detail["orders"]:
            order_list = order_detail["orders"]["order"]
            
            # 获取主订单信息
            main_order_id = order_detail.get("tid", "")
            status = order_detail.get("status", "")
            payment_method = order_detail.get("payment_method", "")
            created = order_detail.get("created", "")
            pay_time = order_detail.get("pay_time", "")
            consign_time = order_detail.get("consign_time", "")
            end_time = order_detail.get("end_time", "")
            
            # 处理每个子订单
            for sub_order in order_list:
                # 获取子订单的交易结束时间，优先使用子订单的end_time，如果没有则使用主订单的end_time
                sub_end_time = sub_order.get("end_time", end_time)

                # 复制主订单信息到子订单
                sub_order_data = {
                    "main_order_id": main_order_id,
                    "tid": main_order_id,
                    "status": sub_order.get("status", status),  # 优先使用子订单状态
                    "payment_method": payment_method,
                    "created": created,
                    "pay_time": pay_time,
                    "consign_time": consign_time,
                    "end_time": sub_end_time,  # 使用子订单的交易结束时间
                    # 添加子订单特有信息
                    "sub_order_id": sub_order.get("oid", ""),
                    "title": sub_order.get("title", ""),
                    "num": sub_order.get("num", 1),
                    "price": float(sub_order.get("price", 0)),
                    "payment": float(sub_order.get("payment", 0)),  # 子订单金额
                    "divide_order_fee": float(sub_order.get("divide_order_fee", 0)),  # 分摊后的实付金额
                    "discount_fee": float(sub_order.get("discount_fee", 0)),  # 优惠金额
                    "refund_status": sub_order.get("refund_status", "NO_REFUND"),
                    "total_fee": float(sub_order.get("total_fee", 0))  # 总金额
                }
                
                sub_orders.append(sub_order_data)
                
        # 如果没有子订单，返回原始订单作为子订单
        if not sub_orders:
            sub_orders.append(order_detail)
            
        return sub_orders
    
    def _fetch_orders_for_month(self, shop, year: int, month: int) -> List[Order]:
        """获取指定店铺和月份的订单"""
        # 设置月份的开始和结束时间
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)
        
        # end_date = datetime(year, month, 2)#todo ..........

        # 格式化为API所需的时间格式
        start_created = start_date.strftime("%Y-%m-%d %H:%M:%S")
        end_created = end_date.strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"查询时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
        # 获取今天的日期
        today_date = date.today().strftime('%Y-%m-%d')
        
        # 获取所有订单ID
        all_order_ids = set()
        page_no = 1
        page_size = 40  # 每页订单数量
        has_next = True
        
        # 循环获取所有页的订单ID
        print(f"正在获取店铺 {shop.shop_name} 的订单ID列表...")
        while has_next:
            try:
                # 使用重试机制调用API，并减少获取的字段数量
                result = self._call_taobao_api_with_retry(
                    shop=shop,
                    method="taobao.trades.sold.get",
                    params={
                        "start_created": start_created,
                        "end_created": end_created,
                        "status": "ALL",  # 获取所有状态的订单
                        "page_no": page_no,
                        "page_size": page_size,
                        # 只获取订单基本信息，详细信息通过get_order_detail单独获取
                        "fields": "tid,status,created"
                    }
                )
                
                # 解析API返回结果
                if "trades_sold_get_response" in result:
                    response = result["trades_sold_get_response"]
                    
                    # 获取订单列表
                    if "trades" in response and "trade" in response["trades"]:
                        trade_list = response["trades"]["trade"]
                        for trade_data in trade_list:
                            all_order_ids.add(str(trade_data.get("tid", "")))
                    
                    # 检查是否有下一页 - 使用总数和页码计算
                    total_results = int(response.get("total_results", 0))
                    print(f"第 {page_no} 页获取到 {len(all_order_ids)} 个订单ID，总订单数: {total_results}")
                    
                    if total_results > page_no * page_size:
                        page_no += 1
                        has_next = True
                    else:
                        has_next = False
                else:
                    print(f"淘宝API返回数据格式错误: {result}")
                    break
                
            except Exception as e:
                print(f"获取淘宝订单第 {page_no} 页失败: {str(e)}")
                break
        
        print(f"总共获取到 {len(all_order_ids)} 个订单ID")
        
        # 将所有订单ID保存到处理表中
        self._save_order_ids_to_process_table(all_order_ids, today_date)
        
        # 获取待处理的订单ID
        pending_order_ids = self._get_pending_order_ids(all_order_ids, today_date)
        
        # 获取订单详情
        orders = []
        total_orders = len(pending_order_ids)
        print(f"需要处理 {total_orders} 个订单（今日待处理状态）")
        
        for index, order_id in enumerate(pending_order_ids, 1):
            try:
                print(f"正在获取订单详情 ({index}/{total_orders}): {order_id}")
                
                # 获取订单详情
                order_detail = self.get_order_detail(shop, order_id)
                if not order_detail:
                    print(f"获取订单 {order_id} 详情失败，跳过")
                    continue
                
                # 提取子订单信息
                sub_orders = self._extract_sub_orders(order_detail)
                
                # 处理每个子订单
                for sub_order in sub_orders:
                    # 转换为Order对象
                    order = self._convert_to_order_object(shop, sub_order)
                    if order:
                        # 保存订单到本地数据库
                        self._save_to_local_db(order)
                        orders.append(order)
                
                # 标记主订单为已处理
                self._mark_order_as_processed(order_id)
                
            except Exception as e:
                print(f"处理订单 {order_id} 失败: {str(e)}")
        
        print(f"成功处理 {len(orders)} 个订单")
        return orders
    
    def _convert_to_order_object(self, shop, trade_data: Dict[str, Any]) -> Optional[Order]:
        """将淘宝API返回的交易数据转换为Order对象"""
        try:
            # 获取订单ID（支持子订单）
            order_id = str(trade_data.get("sub_order_id", trade_data.get("tid", "")))
            main_order_id = str(trade_data.get("main_order_id", trade_data.get("tid", "")))
            
            if not order_id:
                print("订单ID为空，跳过")
                return None

            # 获取订单基本信息
            status_code = trade_data.get("status", "")
            
            # 获取子订单退款状态
            refund_status = trade_data.get("refund_status", "NO_REFUND")
            
            # 处理金额，优先使用子订单的divide_order_fee作为商家应收金额
            divide_order_fee = float(trade_data.get("divide_order_fee", 0))
            payment = float(trade_data.get("payment", 0))
            # 如果是子订单且有divide_order_fee，则使用divide_order_fee作为商家实收
            received_payment = divide_order_fee if "sub_order_id" in trade_data and divide_order_fee > 0 else float(trade_data.get("received_payment", 0))
            
            payment_method_code = trade_data.get("payment_method", "")
            payment_method = "其他"
            if payment_method_code == "wx":
                payment_method = "微信支付"
            elif payment_method_code == "alipay":
                payment_method = "支付宝支付"
            
            # 根据订单状态和退款状态确定最终状态
            payment_status = "UNKNOWN"
            
            # 优先判断退款状态
            if refund_status == "SUCCESS":
                # 退款成功，订单状态为CLOSED
                payment_status = "CLOSED"
            elif status_code == "TRADE_CLOSED":
                # 付款以后用户退款成功，交易自动关闭
                payment_status = "CLOSED"
            elif status_code == "TRADE_CLOSED_BY_TAOBAO":
                # 付款以前，卖家或买家主动关闭交易
                payment_status = "CANCELLED"
            else:
                # 使用基本状态映射
                payment_status = self.ORDER_STATUS_MAP.get(status_code, "UNKNOWN")
            
            # 解析时间
            payment_time = None
            if "pay_time" in trade_data and trade_data["pay_time"]:
                payment_time = datetime.strptime(trade_data["pay_time"], "%Y-%m-%d %H:%M:%S")
            
            shipping_time = None
            if "consign_time" in trade_data and trade_data["consign_time"]:
                shipping_time = datetime.strptime(trade_data["consign_time"], "%Y-%m-%d %H:%M:%S")
            
            # 获取商品名称
            title = trade_data.get("title", "")
            
            # 创建Order对象
            order = Order(
                order_id=order_id,
                payment_status=payment_status,
                payment_time=payment_time,
                shipping_time=shipping_time,
                shop_name=shop.shop_name,
                received_payment=received_payment,
                payment=payment,
                payment_method=payment_method,
            )
            
            # 添加原始数据，方便后续处理
            order.order_data = trade_data
            
            # 添加主订单ID，用于关联子订单
            order.main_order_id = main_order_id
            
            # 添加商品标题
            order.title = title
            
            # 添加退款状态
            order.refund_status = refund_status
            
            return order
        except Exception as e:
            print(f"转换淘宝订单数据失败: {str(e)}")
            return None
    
    def _save_to_local_db(self, order: Order):
        """将订单保存到本地数据库"""
        try:
            # 获取订单数据
            order_data = getattr(order, 'order_data', {})
            
            # 获取主订单ID
            main_order_id = getattr(order, 'main_order_id', order.order_id)
            
            # 获取退款状态
            refund_status = getattr(order, 'refund_status', "NO_REFUND")
            
            # 映射订单状态
            status_code = order.payment_status
            
            # 判断状态值
            if status_code == "PENDING":  # 待付款
                print(f"订单 {order.order_id} 状态为待付款，跳过保存")
                return
            
            # 映射订单状态到枚举值
            status = None
            
            # 根据支付状态和退款状态确定最终状态
            if status_code == "CANCELLED":
                status = OrderStatus.CANCELLED
            elif status_code == "CLOSED" or refund_status == "SUCCESS":
                status = OrderStatus.REFUNDED
            elif status_code == "COMPLETED":
                status = OrderStatus.COMPLETED
            elif status_code == "SHIPPED":
                status = OrderStatus.SHIPPED
            elif status_code in ["PROCESSING"]:
                status = OrderStatus.PAID
            
            if status is None:
                print(f"订单 {order.order_id} 状态 {status_code} 无法映射，跳过保存")
                return
            
            # 确保状态值是有效的枚举值
            if not isinstance(status, OrderStatus):
                raise ValueError(f"无效的订单状态: {status}")

            # 处理订单金额相关数据
            payment = order.received_payment  # 订单金额
            merchant_receive = order.payment  # 商家实收金额
            payment_method = order.payment_method  # 支付渠道
            
            # 根据订单状态设置退款金额
            if status in [OrderStatus.REFUNDED, OrderStatus.CANCELLED]:
                # 如果订单状态是已退款或已取消，退款金额等于商家实收
                refund_amount = merchant_receive
                merchant_receivable = 0  # 应收金额
            else:
                # 其他状态按正常计算退款金额
                refund_amount = payment - merchant_receive
                merchant_receivable = merchant_receive  # 应收金额
            
            # 判断是否已发货
            is_shipped = order.shipping_time is not None
            shipping_time_sec = int(order.shipping_time.timestamp()) if order.shipping_time else None

            # 处理交易结束时间
            end_time_sec = None
            if order_data.get("end_time"):
                try:
                    # 淘宝API返回的时间格式通常是 "2012-04-07 00:00:00"
                    from datetime import datetime
                    end_time_str = order_data.get("end_time")
                    if end_time_str:
                        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
                        end_time_sec = int(end_time_dt.timestamp())
                except Exception as e:
                    print(f"解析交易结束时间失败 ({order.order_id}): {str(e)}")
                    end_time_sec = None

            # 获取结算单数据
            # settlements_query = """
            # SELECT
            #     SUM(settlement_amount) as total_settlement,
            #     MAX(settlement_time) as latest_settlement
            # FROM settlements
            # WHERE order_id = ?
            # """
            # result = self.orders_manager.execute_query_one(settlements_query, (order.order_id,))

            # 从结算单获取回款信息
            received_amount = 0
            payment_date = None
            # if result:
            #     total_settlement, latest_settlement = result
            #     received_amount = total_settlement or 0
            #     payment_date = latest_settlement
            # else:
            received_amount = 0
            payment_date = None

            # 初始化ERP相关金额
            u8c_029_amount = 0
            u8c_515_amount = 0
            t_plus_002_amount = 0
            t_plus_008_amount = 0
            bill_date = ""
            sales_order_total = 0
             # 查询ERP订单信息
            # erp_result = self.order_query.query_single_order(ERPOrder(order_id=order.order_id))
            #
            # print(erp_result)
            #
            #
            #
            #
            # # 根据ERP查询结果更新金额
            # if erp_result.error_code == 0:  # 查询成功
            #     amount = float(erp_result.amount)
            #     bill_date = erp_result.bill_date
            #     # 根据账套ID分配金额
            #     if erp_result.erp_system == "U8C":
            #         if erp_result.account_id == "u8c_029":
            #             u8c_029_amount = amount
            #         elif erp_result.account_id == "u8c_515":
            #             u8c_515_amount = amount
            #     elif erp_result.erp_system == "T+":
            #         if erp_result.account_id == "t_plus_002":
            #             t_plus_002_amount = amount
            #         elif erp_result.account_id == "t_plus_008":
            #             t_plus_008_amount = amount
                
            # 计算销货单合计
            sales_order_total = (u8c_029_amount + u8c_515_amount + t_plus_002_amount + t_plus_008_amount)
            unshipped_amount = merchant_receivable - sales_order_total  # 未发货金额 = 应收金额 - 销货单金额合计

            # 根据店铺名称和支付方式确定支付渠道
            shop_name = order.shop_name
            payment_channel = ""

            # 判断是支付宝还是微信支付
            is_alipay = payment_method == "支付宝支付" or payment_method == "alipay"
            is_wechat = payment_method == "微信支付" or payment_method == "wx"

            # 根据店铺名称和支付方式设置支付渠道
            if "佰酿美酒天猫" in shop_name:
                if is_alipay:
                    payment_channel = "科技支付宝-bainiangmeiiutianmao"
                elif is_wechat:
                    payment_channel = "佰酿美酒天猫平台"
            elif "佰酿科技（天猫旗舰店）" in shop_name:
                if is_alipay:
                    payment_channel = "科技支付宝<EMAIL>"
                elif is_wechat:
                    payment_channel = "天猫旗舰店平台"
            elif "桃公子淘宝店" in shop_name:
                if is_alipay:
                    payment_channel = "科技支付宝-taogongzitaojiuguan"
                elif is_wechat:
                    payment_channel = "桃公子淘宝店平台"
            elif "天猫（法国南部葡萄酒官方旗舰店）" in shop_name:
                if is_alipay:
                    payment_channel = "法南天猫支付宝"
                elif is_wechat:
                    payment_channel = "法南天猫平台余额"
            elif "酒遇喵掌柜" in shop_name:
                if is_alipay:
                    payment_channel = "科技支付宝-jiuyumiaozhanggui"
                elif is_wechat:
                    payment_channel = "酒遇喵掌柜平台余额"
            elif "美尼多" in shop_name:
                if is_alipay:
                    payment_channel = "科技支付卡-shyanjiusuo"
                elif is_wechat:
                    payment_channel = "美尼多天猫旗舰店平台"
            else:
                # 默认支付渠道
                payment_channel = payment_method

            # 构建订单数据
            data = {
                TableFields.ORDER_ID: order.order_id,
                TableFields.MAIN_ORDER_ID: main_order_id,
                TableFields.PLATFORM_TYPE: self.platform_type.value,
                TableFields.PLATFORM_NAME: order.shop_name,
                TableFields.ORDER_STATUS: status,
                TableFields.SHIPPING_TIME: shipping_time_sec,
                TableFields.END_TIME: end_time_sec,  # 交易结束时间
                TableFields.MERCHANT_RECEIVABLE: merchant_receive,  # 商家实收金额
                TableFields.REFUND_AMOUNT: refund_amount,  # 退款金额
                TableFields.ORDER_AMOUNT: payment,  # 订单金额
                TableFields.RECEIVABLE_AMOUNT: merchant_receivable,  # 应收金额
                TableFields.PAYMENT_CHANNEL: payment_channel,
                # erp start
                TableFields.U8C_029_AMOUNT: u8c_029_amount,
                TableFields.U8C_515_AMOUNT: u8c_515_amount,
                TableFields.T_PLUS_002_AMOUNT: t_plus_002_amount,
                TableFields.T_PLUS_008_AMOUNT: t_plus_008_amount,
                TableFields.SALES_ORDER_TOTAL: sales_order_total,
                TableFields.SALES_ORDER_DATE: bill_date,
                # erp end
                TableFields.UNSHIPPED_AMOUNT: unshipped_amount,
                TableFields.RECEIVED_AMOUNT: received_amount,  # 回款金额
                TableFields.PAYMENT_DATE: payment_date,
                TableFields.PENDING_PAYMENT: max(0, merchant_receivable - received_amount)
            }
            
            # 打印调试信息
            print(f"订单 {order.order_id} (主订单: {main_order_id}) 状态映射: {status_code} -> {status.value} 退款状态: {refund_status} 支付方式: {payment_method}, 支付渠道: {payment_channel}")
            
            try:
                # 检查订单是否存在
                exists = self.orders_manager.order_exists(str(order.order_id))
                
                if exists:
                    # 订单存在，执行更新
                    self.orders_manager.update_data(order.order_id, data)
                    print(f"更新订单: {order.order_id} (主订单: {main_order_id})")
                else:
                    # 订单不存在，执行插入
                    self.orders_manager.insert_data(data)
                    print(f"插入订单: {order.order_id} (主订单: {main_order_id})")
                
            except Exception as e:
                print(f"保存订单到本地数据库失败: {str(e)}")
        
        except Exception as e:
            print(f"订单状态处理错误 ({order.order_id}): {str(e)}")
    
    def _call_taobao_api_with_retry(self, shop, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的淘宝API调用"""
        retries = 0
        last_exception = None
        
        while retries < self.max_retries:
            try:
                # 调用原有的API方法
                result = self._call_taobao_api(shop, method, params)
                return result
            except (APIConnectionError, PlatformRunError) as e:
                last_exception = e
                retries += 1
                print(f"API调用失败，正在重试 ({retries}/{self.max_retries}): {str(e)}")
                # 等待一段时间后重试
                time.sleep(self.retry_delay * retries)  # 递增等待时间
        
        # 所有重试都失败后，抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise PlatformRunError(f"API调用失败，已重试 {self.max_retries} 次")
    
    def _call_taobao_api(self, shop, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用淘宝开放平台API"""
        try:
            # 检查shop对象是否有必要的API凭证属性
            if not hasattr(shop, 'app_key') or not hasattr(shop, 'app_secret') or not hasattr(shop, 'access_token'):
                raise ValueError(f"店铺 {shop.shop_name} 缺少API凭证属性")
            
            app_key = shop.app_key
            app_secret = shop.app_secret
            session = shop.access_token
            
            # 准备请求参数
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            request_params = {
                "method": method,
                "app_key": app_key,
                "timestamp": timestamp,
                "format": "json",
                "v": self.API_VERSION,
                "sign_method": "md5",
                "session": session
            }
            
            # 合并业务参数
            request_params.update(params)
            
            # 计算签名
            request_params["sign"] = self._calculate_sign(request_params, app_secret)
            
            # 发送请求
            response = requests.post(self.API_URL, data=request_params)
            
            # 检查响应状态
            if response.status_code != 200:
                raise APIConnectionError(f"淘宝API请求失败，HTTP状态码: {response.status_code}")
            
            # 解析响应数据
            result = response.json()
            
            # 检查是否有错误
            if "error_response" in result:
                error = result["error_response"]
                error_code = error.get("code", "unknown")
                error_msg = error.get("msg", "Unknown error")
                sub_code = error.get("sub_code", "")
                sub_msg = error.get("sub_msg", "")
                
                error_info = f"错误码: {error_code}, 信息: {error_msg}"
                if sub_code:
                    error_info += f", 子错误码: {sub_code}, 子信息: {sub_msg}"
                
                raise PlatformRunError(f"淘宝API返回错误: {error_info}")
            
            return result
        
        except requests.RequestException as e:
            raise APIConnectionError(f"淘宝API连接错误: {str(e)}")
        except Exception as e:
            raise PlatformRunError(f"调用淘宝API失败: {str(e)}")
    
    def _calculate_sign(self, params: Dict[str, Any], app_secret: str) -> str:
        """计算淘宝API请求签名"""
        # 按字母顺序排序参数
        sorted_params = sorted(params.items(), key=lambda x: x[0])
        
        # 拼接参数
        param_str = ""
        for key, value in sorted_params:
            if value is not None and value != "":
                param_str += f"{key}{value}"
        
        # 加上app_secret前后缀
        sign_str = f"{app_secret}{param_str}{app_secret}"
        
        # 计算MD5
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()