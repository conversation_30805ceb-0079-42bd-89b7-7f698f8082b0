from db_manager import DatabaseManager, WebServer, TableFields as table

def main():
    # 初始化数据库
    db_path = "data/sales.db"
    db_manager = DatabaseManager(db_path)

    # 插入数据示例，使用枚举类型的键
    sample_data = {
        table.ORDER_ID: 'ORD001',
        table.PLATFORM_TYPE: '电商平台',
        table.SHOP_NAME: '示例店铺',
        table.ORDER_AMOUNT: 1000.0,
        table.RECEIVABLE_AMOUNT: 900.0
    }
    db_manager.insert_data(sample_data)

    # 更新数据示例，使用枚举类型的键
    update_data = {
        table.REFUND_AMOUNT: 100.0,
        table.RECEIVED_AMOUNT: 800.0
    }
    db_manager.update_data('ORD001', update_data)

    # 插入数据示例，使用字符串键（仍然有效）
    sample_data_str_keys = {
        'order_id': 'ORD002',
        'platform_type': '电商平台2',
        'shop_name': '示例店铺2'
    }
    db_manager.insert_data(sample_data_str_keys)

    # 查询数据示例
    result = db_manager.query_data(page=1, page_size=10)
    print("查询结果:", result)

    # 启动Web服务
    web_server = WebServer(db_manager)
    web_server.run()

if __name__ == '__main__':
    main()