from typing import List

import requests

from shop_manager import PlatformType, ShopManager, PlatformName


class Shop:
    def __init__(self, shop_id, shop_name):
        self.shop_id = shop_id
        self.shop_name = shop_name

    def __repr__(self):
        return f"Shop(shop_id={self.shop_id}, shop_name='{self.shop_name}')"

    def get_shop_info(self):
        """
        :return: 返回店铺详情
        """

        url = 'https://callback.vinehoo.com/xiaohongshu-sync/xiaohongshu/v3/shopInfo'
        post_data = {
            'shop_id': self.shop_id,
        }

        resp = requests.post(url, post_data).json()
        if resp.get('error_code', -1) != 0:
            return None

        print(resp.get('data', None))

        return resp.get('data', None)


class PlatformShopConfig:
    def __init__(self):
        # 配置信息，按照平台分类存储多个店铺
        self.shopManager = ShopManager()
        self.platforms = {
            PlatformType.XIAOHONGSHU.value: [
                self.to_Shop(PlatformName.XHS_YUNWINE),
                self.to_Shop(PlatformName.XHS_MULANDO),
                self.to_Shop(PlatformName.XHS_WEI_DISTILLERY),
                self.to_Shop(PlatformName.XHS_BROWN_BROTHERS),
                # Shop('62b98b750d601800010dc853', '行吟信息科技（武汉）有限公司(云酒网小红书店）'),
                # Shop('650a60e17fa15200013acf16', '木兰朵-小红书'),
                # Shop('65113b63effd830001ca90e0', '小红书-威哥蒸馏所'),
                # Shop('653b599dbffe730001559bd6', '小红书-Brown Brothers布琅兄弟'),
            ],
            PlatformType.TAOBAO.value: [
                self.to_Shop(PlatformName.TB_BAINIANG_WINE),
                self.to_Shop(PlatformName.TB_BAINIANG_FLAG),
                self.to_Shop(PlatformName.TB_TAOGONGZI),
                self.to_Shop(PlatformName.TB_FRANCE_SOUTH),
                self.to_Shop(PlatformName.TB_JIUYUMIAO),
                self.to_Shop(PlatformName.TB_MEINIDUO),
                # Shop('227734657', '佰酿美酒天猫'),
                # Shop('419938814', '佰酿科技（天猫旗舰店）'),
                # Shop('566768114', '桃公子淘宝店'),
                # Shop('558695549', '天猫（法国南部葡萄酒官方旗舰店）'),
                # Shop('452890329', '酒遇喵掌柜'),
                # Shop('541276454', '美尼多天猫旗舰店'),
            ],
            PlatformType.DOUYIN.value: [
                self.to_Shop(PlatformName.DOUYIN_JIUYUN_FLAGSHIP),
                self.to_Shop(PlatformName.DOUYIN_MULANDO_FLAGSHIP),
                # Shop('68119621', '酒云网抖店-卖场旗舰店'),
                # Shop('100620515', '木兰朵-抖音旗舰店'),
            ],
            PlatformType.OTHER.value: [
                self.to_Shop(PlatformName.DEWU_BAINIANG),
                # Shop('5946418', '佰酿科技（得物APP）'),
            ]
        }

    def to_Shop(self, shop_name: PlatformName) -> Shop:
        shopInfo = self.shopManager.get_shop(platform_name=shop_name)
        if shop_name is not None:
            return Shop(shop_id=shopInfo.shop_id, shop_name=shop_name.value)

    def get_shops_by_platform(self, platform_name) -> List[Shop]:
        """
        根据平台名称获取所有店铺信息
        :param platform_name: 平台名称（字符串）
        :return: 该平台下的所有店铺列表
        """

        return self.platforms.get(platform_name, [])
