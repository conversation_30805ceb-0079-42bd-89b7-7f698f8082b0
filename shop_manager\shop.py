import json
from typing import List, Dict, Optional
from .constants import Entity, PlatformType, PlatformName

class ShopInfo:
    """店铺信息类"""
    def __init__(
        self,
        entity: Entity,
        platform_type: PlatformType,
        platform_name: PlatformName,
        backend_name: str,
        payment_accounts: List[str],
        bank_subjects: List[str],
        shop_id: str
    ):
        if len(payment_accounts) != len(bank_subjects):
            raise ValueError("收款账号和银行科目数量必须一致")
        
        self.entity = entity
        self.platform_type = platform_type
        self.platform_name = platform_name
        self.backend_name = backend_name
        self.payment_accounts = payment_accounts
        self.bank_subjects = bank_subjects
        self.shop_id = shop_id

    def __str__(self) -> str:
        return f"Shop({self.entity.value} - {self.platform_name.value} - {self.backend_name})"

class ShopManager:
    """店铺管理类（单例模式）"""
    _instance = None
    _default_config_path = "shop_manager/config.json"  # 默认配置文件路径

    def __new__(cls, config_path: str = None):
        if cls._instance is None:
            cls._instance = super(ShopManager, cls).__new__(cls)
            # 初始化时自动加载配置文件
            config = config_path or cls._default_config_path
            cls._instance._initialize(config)
        return cls._instance

    def _initialize(self, config_path: str) -> None:
        """初始化方法"""
        self.shops: Dict[str, ShopInfo] = {}
        self.load_from_config(config_path)

    def load_from_config(self, config_path: str) -> None:
        """从配置文件加载店铺数据"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for shop_data in config_data.get('shops', []):
                try:
                    shop = ShopInfo(
                        entity=Entity(shop_data['entity']),
                        platform_type=PlatformType(shop_data['platform_type']),
                        platform_name=PlatformName(shop_data['platform_name']),
                        backend_name=shop_data['backend_name'],
                        payment_accounts=shop_data['payment_accounts'],
                        bank_subjects=shop_data['bank_subjects'],
                        shop_id=shop_data['shop_id']
                    )
                    self.add_shop(shop)
                except (KeyError, ValueError) as e:
                    print(f"加载店铺数据时出错: {e}")
                    continue
                    
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 未找到")
        except json.JSONDecodeError:
            raise ValueError(f"配置文件 {config_path} 格式错误")

    def add_shop(self, shop: ShopInfo) -> None:
        """添加店铺信息"""
        key = f"{shop.platform_name.value}_{shop.backend_name}"
        self.shops[key] = shop

    def get_by_platform_name(self, platform_name: PlatformName) -> List[Dict[str, List[str]]]:
        """通过平台名称查询收款账号和银行科目"""
        results = []
        for shop in self.shops.values():
            if shop.platform_name == platform_name:
                results.append({
                    "backend_name": shop.backend_name,
                    "payment_accounts": shop.payment_accounts,
                    "bank_subjects": shop.bank_subjects
                })
        return results

    def get_all_shops(self) -> List[ShopInfo]:
        """获取所有店铺信息"""
        return list(self.shops.values())

    def get_shop(self, platform_name: PlatformName) -> Optional[ShopInfo]:
        """通过平台名称获取店铺信息
        
        Args:
            platform_name: PlatformName枚举类型的平台名称
            
        Returns:
            Optional[ShopInfo]: 如果找到店铺则返回ShopInfo对象，否则返回None
        """
        for shop in self.shops.values():
            if shop.platform_name == platform_name:
                return shop
        return None

    def get_by_platform_type(self, platform_type: PlatformType) -> List[ShopInfo]:
        """通过平台类型获取所有相关店铺信息

        Args:
            platform_type: PlatformType枚举类型的平台类型

        Returns:
            List[ShopInfo]: 该平台类型下的所有店铺信息列表
        """
        return [shop for shop in self.shops.values() if shop.platform_type == platform_type]