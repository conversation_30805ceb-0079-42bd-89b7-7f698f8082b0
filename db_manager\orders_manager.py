import sqlite3
import os
from typing import Dict, Any, List, Optional
from enum import Enum
from .table_fields import TableFields
from .base_db_manager import BaseDBManager
from .order_status import OrderStatus

class OrdersManager(BaseDBManager):
    def __init__(self, db_path: str):
        """初始化数据库"""
        self.db_path = db_path
        self._initialize_database()

    def _initialize_database(self):
        """创建数据库和表"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 创建表
            cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS sales_data (
                    order_id TEXT PRIMARY KEY,
                    platform_type TEXT,              -- 平台类型
                    platform_name TEXT,              -- 平台名称
                    main_order_id TEXT,              -- 主订单号
                    order_status TEXT NOT NULL CHECK(order_status IN ({OrderStatus.get_valid_statuses()})), -- 订单状态
                    shipping_time INTEGER,           -- 发货时间（时间戳）
                    end_time INTEGER,                -- 交易结束时间（时间戳）
                    merchant_receivable REAL,        -- 商家应收金额
                    refund_amount REAL,              -- 退款金额
                    order_amount REAL,               -- 订单金额
                    receivable_amount REAL,          -- 应收金额
                    u8c_029_amount REAL,            -- U8C-029金额
                    u8c_515_amount REAL,            -- U8C-515金额
                    t_plus_002_amount REAL,         -- T+002金额
                    t_plus_008_amount REAL,         -- T+008金额
                    sales_order_total REAL,         -- 销货单合计
                    sales_order_date TEXT,        -- 销货单日期
                    unshipped_amount REAL,          -- 本月未发货金额
                    received_amount REAL,           -- 已回款金额
                    payment_date TEXT,              -- 回款日期
                    payment_channel TEXT,           -- 收款渠道
                    pending_payment REAL            -- 待回款
                )
            ''')

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_id ON sales_data(order_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_status ON sales_data(order_status)')
            conn.commit()

    def _normalize_keys(self, data: Dict[Any, Any], valid_fields: set) -> Dict[str, Any]:
        """将枚举类型的键或字符串键转换为字符串类型的键"""
        normalized_data = {}

        for key, value in data.items():
            if isinstance(key, TableFields):
                if key.value in valid_fields:
                    # 如果是订单状态字段，验证状态值是否合法
                    if str(key) == 'order_status' and not isinstance(value, OrderStatus):
                        raise ValueError(f"Order status must be an OrderStatus enum value, got {value}")
                    # 如果是订单状态，存储其值
                    normalized_data[str(key)] = value.value if isinstance(value, OrderStatus) else value
                else:
                    raise ValueError(f"Invalid field name: {key}. Must be one of {valid_fields}")
            elif isinstance(key, str):
                if key in valid_fields:
                    if key == 'order_status' and not isinstance(value, OrderStatus):
                        raise ValueError(f"Order status must be an OrderStatus enum value, got {value}")
                    normalized_data[key] = value.value if isinstance(value, OrderStatus) else value
                else:
                    raise ValueError(f"Invalid field name: {key}. Must be one of {valid_fields}")
            else:
                raise TypeError(f"Invalid key type: {type(key)}. Must be TableFields or str.")
        return normalized_data

    def _get_valid_fields(self) -> set:
        """获取有效字段集合"""
        return {field.value for field in TableFields}

    def insert_data(self, data: Dict[TableFields, Any]) -> bool:
        """插入数据，支持枚举类型的键"""
        try:
            # 规范化键
            normalized_data = self._normalize_keys(data, self._get_valid_fields())

            # 准备字段名和值
            fields = list(normalized_data.keys())
            values = [normalized_data[field] for field in fields]
            placeholders = ','.join(['?' for _ in fields])
            fields_str = ','.join(fields)

            query = f'INSERT OR IGNORE INTO sales_data ({fields_str}) VALUES ({placeholders})'
            self.execute_query(query, tuple(values))
            return True
        except Exception as e:
            print(f"Insert error: {e}")
            return False

    def update_data(self, order_id: str, data: Dict[TableFields, Any]) -> bool:
        """更新数据，支持枚举类型的键"""
        try:
            # 规范化键
            normalized_data = self._normalize_keys(data, self._get_valid_fields())

            # 准备更新语句
            set_clause = ','.join([f'{field}=?' for field in normalized_data.keys()])
            values = list(normalized_data.values())
            values.append(order_id)

            query = f'UPDATE sales_data SET {set_clause} WHERE order_id=?'
            self.execute_query(query, tuple(values))
            return True
        except Exception as e:
            print(f"Update error: {e}")
            return False

    def order_exists(self, order_id: str) -> bool:
        """检查指定的订单ID是否存在于数据库中"""
        try:
            query = "SELECT COUNT(*) FROM sales_data WHERE order_id=?"
            result = self.execute_query_one(query, (order_id,))
            return result[0] > 0 if result else False
        except Exception as e:
            print(f"Error checking order existence: {e}")
            return False

    def query_data(self, page: int = 1, page_size: int = 10,
                  filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """查询数据，支持分页和过滤"""
        try:
            # 构建查询条件
            where_clause = ''
            params = []
            if filters:
                conditions = []
                for field, value in filters.items():
                    # 对于日期字段，使用LIKE查询来匹配部分日期
                    if field in ['sales_order_date', 'payment_date', 'shipping_time'] and value:
                        conditions.append(f'{field} LIKE ?')
                        params.append(f'%{value}%')
                    else:
                        conditions.append(f'{field}=?')
                        params.append(value)
                where_clause = ' WHERE ' + ' AND '.join(conditions)

            base_query = 'SELECT * FROM sales_data'
            return self.paginate_query(base_query, page, page_size, where_clause, params)
        except Exception as e:
            print(f"Query error: {e}")
            return {'error': str(e)}