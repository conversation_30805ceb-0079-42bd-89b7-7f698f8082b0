# order_fetcher/order_fetcher.py
from datetime import datetime
from typing import List, Union, Optional
import os
import time

from sqlalchemy import create_engine, text

import shop_manager.constants  # 导入外部定义的PlatformName
from data_folder_utils import FolderManager
from db_manager import OrdersManager, TableFields
from shop_manager.constants import PlatformType, PlatformName
from .exceptions import InvalidPlatformError
from .models import OrderData
from db_manager.order_status import OrderStatus  # 添加这行导入

import oss2
from dotenv import load_dotenv

# 加载.env文件中的配置
load_dotenv()

class OrderFetcher:
    # 阿里云OSS配置
    OSS_CONFIG = {
        'ACCESSKEYID': os.getenv('OSS_ACCESSKEYID'),
        'ACCESSKEYSECRET': os.getenv('OSS_ACCESSKEYSECRET'),
        'ENDPOINT': os.getenv('OSS_ENDPOINT'),
        'BUCKET': os.getenv('OSS_BUCKET'),
        'ALIURL': os.getenv('OSS_ALIURL'),
        'ROLEARN': os.getenv('OSS_ROLEARN'),
        'ROLESESSIONNAME': os.getenv('OSS_ROLESESSIONNAME'),
        'REGIONID': os.getenv('OSS_REGIONID'),
        'CALLBACK_URL': os.getenv('OSS_CALLBACK_URL')
    }
    
    def __init__(self):
        self.platform_handlers = {}
        # 初始化订单管理器
        self.orders_manager = OrdersManager(FolderManager.get_db_file_path())
        # 数据库连接字符串
        self.db_url = os.getenv('DB_URL')
        if not self.db_url:
            raise ValueError("环境变量DB_URL未设置，请检查.env文件")

    def upload_file_to_oss(self, local_file_path: str, target_dir: str = None) -> str:
        """
        将本地文件上传到阿里云OSS，如果文件重名则覆盖
        
        Args:
            local_file_path: 本地文件路径
            target_dir: 目标目录，默认为None，即上传到根目录
        
        Returns:
            上传后的文件URL
        
        Raises:
            Exception: 上传失败时抛出异常
        """
        if not os.path.exists(local_file_path):
            raise FileNotFoundError(f"本地文件不存在: {local_file_path}")
        
        try:
            # 初始化OSS客户端
            auth = oss2.Auth(self.OSS_CONFIG['ACCESSKEYID'], self.OSS_CONFIG['ACCESSKEYSECRET'])
            bucket = oss2.Bucket(auth, self.OSS_CONFIG['ENDPOINT'], self.OSS_CONFIG['BUCKET'])
            
            # 获取原始文件名
            file_name = os.path.basename(local_file_path)
            
            # 确定目标路径
            if target_dir:
                # 确保目录路径格式正确
                if not target_dir.endswith('/'):
                    target_dir += '/'
                # 去除开头可能有的斜杠
                if target_dir.startswith('/'):
                    target_dir = target_dir[1:]
                    
                object_name = f"{target_dir}{file_name}"
            else:
                object_name = file_name
            
            # 上传文件
            result = bucket.put_object_from_file(object_name, local_file_path)
            
            # 检查上传是否成功
            if result.status != 200:
                raise Exception(f"文件上传失败，状态码: {result.status}")
            
            # 构建文件URL
            file_url = f"{self.OSS_CONFIG['ALIURL']}/{object_name}"
            
            print(f"文件上传成功: {file_url}")
            return file_url
            
        except Exception as e:
            error_msg = f"上传文件到OSS失败: {str(e)}"
            print(error_msg)
            raise Exception(error_msg)

    def _save_to_local_db(self, order_data: OrderData, platform: shop_manager.constants.PlatformName,
                          platform_type: PlatformType):
        """保存订单数据到本地SQLite数据库"""
        
        # 获取订单状态
        sub_order_status = getattr(order_data, 'sub_order_status', 1)  # 默认为已支付
        refund_status = getattr(order_data, 'refund_status', 0)  # 默认为无退款
        
        # 过滤掉不需要的订单状态
        if sub_order_status in [0, 5]:  # 待支付、已拒收
            print(f"订单 {order_data.order_id} 状态为 {sub_order_status}，跳过保存")
            return
        
        # 映射订单状态
        status = None
        if refund_status == 2:  # 已退款
            status = OrderStatus.REFUNDED
        elif sub_order_status == 4:  # 已取消
            status = OrderStatus.CANCELLED
        elif sub_order_status == 3:  # 已完成
            status = OrderStatus.COMPLETED
        elif sub_order_status == 2:  # 已发货
            status = OrderStatus.SHIPPED
        elif sub_order_status == 1:  # 已支付
            status = OrderStatus.PAID
        
        if status is None:
            print(f"订单 {order_data.order_id} 状态 {sub_order_status} 无法映射，跳过保存")
            return

        data = {
            TableFields.ORDER_ID: order_data.order_id,
            TableFields.PLATFORM_TYPE: platform_type.value,
            TableFields.PLATFORM_NAME: platform.value,
            TableFields.ORDER_STATUS: status,  # 添加订单状态字段
            # TableFields.MERCHANT_RECEIVABLE: order_data.payment_amount if hasattr(order_data, 'payment_amount') else 0, #商家应收金额
            # TableFields.ORDER_AMOUNT: order_data.payment_amount if hasattr(order_data, 'payment_amount') else 0, #订单金额
            # TableFields.RECEIVABLE_AMOUNT: order_data.payment_amount if hasattr(order_data, 'payment_amount') else 0, #应收金额
            # TableFields.PAYMENT_DATE: order_data.payment_time.strftime('%Y-%m-%d') if order_data.payment_time else None, #回款日期
            # TableFields.PAYMENT_CHANNEL: None, #收款渠道
            # TableFields.UNSHIPPED_AMOUNT: order_data.payment_amount if not order_data.shipping_time else 0, #本月未发货金额
            # TableFields.PENDING_PAYMENT: order_data.payment_amount if hasattr(order_data, 'payment_amount') else 0, #待回款
            # TableFields.REFUND_AMOUNT: 0,  # 退款金额
        }
        # sales_order_total REAL,         -- 销货单合计
        # received_amount REAL,           -- 已回款金额
        # u8c_029_amount REAL,            -- U8C-029金额
        # u8c_515_amount REAL,            -- U8C-515金额
        # t_plus_002_amount REAL,         -- T+002金额
        # t_plus_008_amount REAL,         -- T+008金额

        try:
            # 使用 order_exists 方法检查订单是否存在
            exists = self.orders_manager.order_exists(str(order_data.order_id))

            if exists:
                # 订单存在，执行更新
                self.orders_manager.update_data(order_data.order_id, data)
                print(f"更新订单: {order_data.order_id}")
            else:
                # 订单不存在，执行插入
                self.orders_manager.insert_data(data)
                print(f"插入订单: {order_data.order_id}")

        except Exception as e:
            raise Exception(f"Error saving to local DB: {str(e)}")

    def get_orders_by_month(
            self,
            platform: PlatformName,
            platform_type: PlatformType,
            year: int,
            month: int
    ) -> List[OrderData]:
        """
        根据平台名称获取指定年月的所有订单数据
        
        Args:
            platform: 平台名称 (Enum)
            platform_type: 平台类型 (Enum)
            year: 年份
            month: 月份 (1-12)
            
        Returns:
            List[OrderData]: 订单数据列表
            
        Raises:
            InvalidPlatformError: 平台名称无效
            ValueError: 年月参数无效
        """
        # 验证平台名称
        if not isinstance(platform, PlatformName):
            raise InvalidPlatformError(f"Invalid platform: {platform}")

        # 验证年月
        if not (1 <= month <= 12):
            raise ValueError(f"Invalid month: {month}")
        if year < 2000:  # 假设订单系统从2000年开始
            raise ValueError(f"Invalid year: {year}")

        # 这里应该是实际的订单获取逻辑
        # 以下是示例实现
        orders = self._fetch_orders_from_platform(platform, platform_type, year, month)
        return orders

    def get_orders_by_ids(
            self,
            platform: PlatformName,
            platform_type: PlatformType,
            order_ids: Union[str, List[str]]
    ) -> Union[OrderData, List[OrderData]]:
        """
        根据订单号获取订单数据，支持批量查询
        
        Args:
            platform: 平台名称 (Enum)
            platform_type: 平台类型 (Enum)
            order_ids: 订单号，可以是单个字符串或字符串列表
            
        Returns:
            OrderData or List[OrderData]: 单个订单数据或订单数据列表
            
        Raises:
            InvalidPlatformError: 平台名称无效
            OrderNotFoundError: 订单未找到
        """
        if not isinstance(platform, PlatformName):
            raise InvalidPlatformError(f"Invalid platform: {platform}")

        # 转换为列表统一处理
        ids = [order_ids] if isinstance(order_ids, str) else order_ids

        if not ids:
            return []

        # 这里应该是实际的订单查询逻辑
        # 以下是示例实现
        results = self._fetch_orders_by_ids_from_platform(platform, platform_type, ids)

        if isinstance(order_ids, str):
            return results[0] if results else None
        return results

    def _fetch_orders_from_platform(
            self,
            platform: PlatformName,
            platform_type: PlatformType,
            year: int,
            month: int
    ) -> List[OrderData]:
        """实际从平台获取订单的实现"""
        # 计算查询的时间范围并转换为时间戳
        begin_time = int(datetime(year, month, 1).timestamp())
        if month == 12:
            end_time = int(datetime(year + 1, 1, 1).timestamp()) - 1
        else:
            end_time = int(datetime(year, month + 1, 1).timestamp()) - 1

        # 创建数据库连接
        engine = create_engine(self.db_url)

        # SQL查询语句
        sql = """
            SELECT 
                o.payment_amount,
                o.sub_order_status,
                o.sub_order_no,
                o.payment_time,
                o.delivery_time,
                o.refund_status,
                COALESCE(m.payment_method, -1) as payment_method
            FROM vh_tripartite_order o
            LEFT JOIN vh_order_main m ON o.main_order_id = m.id
            WHERE o.store_name = :platform
            AND o.payment_time BETWEEN :begin_time AND :end_time
        """

        def get_payment_status(sub_order_status: int, refund_status: int) -> str:
            # 如果有退款
            if refund_status > 0:
                refund_status_map = {
                    1: "退款中",
                    2: "已退款",
                    3: "退款失败"
                }
                return refund_status_map.get(refund_status, "未知退款状态")

            # 如果没有退款，返回订单状态
            status_map = {
                0: "待支付",
                1: "已支付",
                2: "已发货",
                3: "已完成",
                4: "已取消",
                5: "已拒收"
            }
            return status_map.get(sub_order_status, "未知状态")

        try:
            with engine.connect() as connection:
                result = connection.execute(
                    text(sql),
                    {
                        "platform": platform.value,
                        "begin_time": begin_time,
                        "end_time": end_time
                    }
                )

                orders = []
                for row in result:
                    # 将时间戳转回 datetime
                    payment_time = datetime.fromtimestamp(row.payment_time) if row.payment_time else None
                    shipping_time = datetime.fromtimestamp(row.delivery_time) if row.delivery_time else None

                    # 创建 OrderData 对象，添加状态字段
                    order_data = OrderData(
                        order_id=row.sub_order_no,
                        payment_status=get_payment_status(row.sub_order_status, row.refund_status),
                        payment_time=payment_time,
                        shipping_time=shipping_time,
                        payment_amount=float(row.payment_amount) if row.payment_amount else 0,
                        sub_order_status=row.sub_order_status,  # 添加订单状态
                        refund_status=row.refund_status  # 添加退款状态
                    )

                    # 保存到本地SQLite数据库
                    self._save_to_local_db(order_data, platform, platform_type)

                    orders.append(order_data)

                return orders

        except Exception as e:
            # 这里应该添加适当的错误处理
            raise Exception(f"Failed to fetch orders: {str(e)}")

    def _fetch_orders_by_ids_from_platform(
            self,
            platform: PlatformName,
            platform_type: PlatformType,
            order_ids: List[str]
    ) -> List[OrderData]:
        """实际根据订单号从平台获取订单的实现"""
        # 创建数据库连接
        engine = create_engine(self.db_url)

        # SQL查询语句
        sql = """
            SELECT 
                    o.payment_amount,
                    o.sub_order_status,
                    o.sub_order_no,
                    o.payment_time,
                    o.delivery_time,
                    o.refund_status,
                COALESCE(m.payment_method, -1) as payment_method
            FROM vh_tripartite_order o
            LEFT JOIN vh_order_main m ON o.main_order_id = m.id
            WHERE o.store_name = :platform
            AND o.sub_order_no IN :order_ids
        """

        def get_payment_status(sub_order_status: int, refund_status: int) -> str:
            # 如果有退款
            if refund_status > 0:
                refund_status_map = {
                    1: "退款中",
                    2: "已退款",
                    3: "退款失败"
                }
                return refund_status_map.get(refund_status, "未知退款状态")

            # 如果没有退款，返回订单状态
            status_map = {
                0: "待支付",
                1: "已支付",
                2: "已发货",
                3: "已完成",
                4: "已取消",
                5: "已拒收"
            }
            return status_map.get(sub_order_status, "未知状态")

        try:
            with engine.connect() as connection:
                result = connection.execute(
                    text(sql),
                    {
                        "platform": platform.value,
                        "order_ids": tuple(order_ids)
                    }
                )

                orders = []
                for row in result:
                    # 将时间戳转回 datetime
                    payment_time = datetime.fromtimestamp(row.payment_time) if row.payment_time else None
                    shipping_time = datetime.fromtimestamp(row.delivery_time) if row.delivery_time else None

                    # 创建 OrderData 对象，添加状态字段
                    order_data = OrderData(
                        order_id=row.sub_order_no,
                        payment_status=get_payment_status(row.sub_order_status, row.refund_status),
                        payment_time=payment_time,
                        shipping_time=shipping_time,
                        payment_amount=float(row.payment_amount) if row.payment_amount else 0,
                        sub_order_status=row.sub_order_status,  # 添加订单状态
                        refund_status=row.refund_status  # 添加退款状态
                    )

                    # 保存到本地SQLite数据库
                    self._save_to_local_db(order_data, platform, platform_type)

                    orders.append(order_data)

                return orders

        except Exception as e:
            # 这里应该添加适当的错误处理
            raise Exception(f"Failed to fetch orders: {str(e)}")

    def sync_sales_data_to_mysql(self, year: int, month: int, platforms: Union[PlatformType, List[PlatformType]]):
        """
        将SQLite中的sales_data数据同步到MySQL数据库
        
        Args:
            year: 年份
            month: 月份 (1-12)
            platforms: 平台类型，可以是单个PlatformType或PlatformType列表
            
        Returns:
            同步的记录数
        """
        # 转换为列表统一处理
        platform_list = [platforms] if not isinstance(platforms, list) else platforms
        
        # 验证年月
        if not (1 <= month <= 12):
            raise ValueError(f"无效的月份: {month}")
        if year < 2000:
            raise ValueError(f"无效的年份: {year}")
        
        # 格式化月份为"YYYY-MM"格式
        month_str = f"{year}-{month:02d}"
        
        # 创建MySQL数据库连接
        mysql_engine = create_engine(self.db_url)
        
        # 获取平台类型列表用于SQL查询
        platform_types = [p.value for p in platform_list]
        
        total_synced = 0
        
        try:
            # 从SQLite读取sales_data数据
            all_records = []
            page = 1
            page_size = 1000
            
            while True:
                # 使用query_data方法分页获取数据
                result = self.orders_manager.query_data(
                    page=page,
                    page_size=page_size,
                    filters={'platform_type': platform_types[0]}  # 先获取第一个平台类型的数据
                )
                
                if 'error' in result:
                    raise Exception(f"查询数据失败: {result['error']}")
                
                records = result.get('data', [])
                if not records:
                    break
                    
                all_records.extend(records)
                
                # 如果返回的记录数小于页大小，说明已经到达最后一页
                if len(records) < page_size:
                    break
                    
                page += 1
            
            # 如果有多于一个平台类型，继续获取其他平台类型的数据
            for platform_type in platform_types[1:]:
                page = 1
                while True:
                    result = self.orders_manager.query_data(
                        page=page,
                        page_size=page_size,
                        filters={'platform_type': platform_type}
                    )
                    
                    if 'error' in result:
                        raise Exception(f"查询数据失败: {result['error']}")
                    
                    records = result.get('data', [])
                    if not records:
                        break
                        
                    all_records.extend(records)
                    
                    if len(records) < page_size:
                        break
                        
                    page += 1
            
            if not all_records:
                print(f"没有找到平台类型{platform_types}的数据")
                return 0
            
            # 将记录转换为字典列表
            records = []
            for row in all_records:
                record = dict(row)
                
                # 添加month字段
                record['month'] = month_str
                
                # 确保数值字段不为None
                for field in ['merchant_receivable', 'refund_amount', 'order_amount',
                             'receivable_amount', 'u8c_029_amount', 'u8c_515_amount',
                             't_plus_002_amount', 't_plus_008_amount', 'sales_order_total',
                             'unshipped_amount', 'received_amount', 'pending_payment']:
                    if record.get(field) is None:
                        record[field] = 0.0

                # 确保时间字段格式正确
                if record.get('end_time') is not None:
                    # end_time在SQLite中存储为时间戳，需要转换为MySQL的datetime格式
                    try:
                        from datetime import datetime
                        end_time_timestamp = record.get('end_time')
                        if end_time_timestamp and end_time_timestamp != 0:
                            # 确保时间戳是有效的
                            if isinstance(end_time_timestamp, (int, float)):
                                end_time_dt = datetime.fromtimestamp(end_time_timestamp)
                                record['end_time'] = end_time_dt
                            else:
                                print(f"无效的end_time时间戳格式: {end_time_timestamp} (类型: {type(end_time_timestamp)})")
                                record['end_time'] = None
                        else:
                            record['end_time'] = None
                    except Exception as e:
                        print(f"转换end_time失败 (订单: {record.get('order_id')}): {str(e)}")
                        record['end_time'] = None
                else:
                    record['end_time'] = None
                
                records.append(record)
            
            if not records:
                print("没有匹配的记录需要同步")
                return 0
            
            with mysql_engine.connect() as connection:
                # 开始事务
                with connection.begin():
                    # 删除目标表中相同月份和平台类型的数据
                    delete_sql = """
                    DELETE FROM vh_sales_data 
                    WHERE month = :month AND platform_type IN :platform_types
                    """
                    connection.execute(
                        text(delete_sql),
                        {"month": month_str, "platform_types": tuple(platform_types)}
                    )
                    
                    # 批量插入数据
                    batch_size = 1000  # 每批插入的记录数
                    
                    for i in range(0, len(records), batch_size):
                        batch = records[i:i+batch_size]
                        
                        # 构建插入语句
                        insert_fields = list(batch[0].keys())
                        placeholders = ', '.join([f':{field}' for field in insert_fields])
                        
                        insert_sql = f"""
                        INSERT INTO vh_sales_data ({', '.join(insert_fields)})
                        VALUES ({placeholders})
                        """
                        
                        # 执行批量插入
                        connection.execute(text(insert_sql), batch)
                        
                        print(f"已同步 {len(batch)} 条记录 (批次 {i//batch_size + 1})")
                    
                    total_synced = len(records)
                    
            print(f"成功同步 {total_synced} 条记录到MySQL，月份: {month_str}，平台类型: {platform_types}")
            return total_synced
            
        except Exception as e:
            print(f"同步数据到MySQL失败: {str(e)}")
            raise Exception(f"同步数据到MySQL失败: {str(e)}")
            
    def update_receivable_from_settlements(self):
        """
        根据结算记录更新销售数据的应收、已收和待回款金额
        
        步骤:
        1. 从settlements表获取所有结算记录，按order_id分组计算settlement_amount总和
        2. 从MySQL获取匹配订单的已回款金额(received_amount)
        3. 更新MySQL中的应收金额(receivable_amount)和待回款金额(pending_payment)
        
        Returns:
            int: 更新的记录数量
        """
        try:
            # 记录开始时间
            start_time = time.time()
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始查询结算数据...")
            
            # 从settlements表获取数据并按order_id分组，同时获取最新的结算时间
            settlement_query = """
            WITH latest_settlement AS (
                SELECT 
                    order_id,
                    MAX(settlement_time) as latest_time
                FROM 
                    settlements
                GROUP BY 
                    order_id
            )
            SELECT 
                s.order_id, 
                SUM(s.settlement_amount) as total_settlement_amount,
                ls.latest_time as latest_settlement_time
            FROM 
                settlements s
            JOIN 
                latest_settlement ls ON s.order_id = ls.order_id
            GROUP BY 
                s.order_id
            """
            
            settlement_results = self.orders_manager.execute_query(settlement_query)
            
            if not settlement_results:
                print("没有找到结算记录")
                return 0
            
            # 将结果转换为字典，键为order_id，值为(总结算金额, 最新结算时间)的元组
            settlement_dict = {row[0]: (row[1], row[2]) for row in settlement_results}
            total_settlements = len(settlement_dict)
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 共找到 {total_settlements} 条结算记录")
            
            # 获取所有结算记录中的order_id列表
            order_ids = list(settlement_dict.keys())
            
            # 创建MySQL数据库连接
            mysql_engine = create_engine(self.db_url)
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 已连接MySQL数据库, 准备开始更新")
            
            # 批量处理，每次处理1000个订单
            batch_size = 1000
            updated_count = 0
            total_batches = (total_settlements + batch_size - 1) // batch_size  # 计算总批次数
            
            print("\n开始分批处理数据:")
            print("-" * 60)
            print(f"| 总记录数: {total_settlements} | 批量大小: {batch_size} | 总批次: {total_batches} |")
            print("-" * 60)
            
            for i in range(0, len(order_ids), batch_size):
                batch_num = i // batch_size + 1
                progress_percent = (batch_num / total_batches) * 100
                
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 处理批次 {batch_num}/{total_batches} ({progress_percent:.1f}%)")
                batch_start_time = time.time()
                
                batch_order_ids = order_ids[i:i+batch_size]
                batch_count = len(batch_order_ids)
                
                # 从MySQL获取匹配的订单数据
                mysql_query = """
                SELECT 
                    order_id, 
                    COALESCE(receivable_amount, 0) as receivable_amount
                FROM 
                    vh_sales_data
                WHERE 
                    order_id IN :order_ids
                """
                
                with mysql_engine.connect() as connection:
                    # 查询订单的已回款金额
                    print(f"  - 查询MySQL中的 {batch_count} 个订单...")
                    result = connection.execute(
                        text(mysql_query),
                        {"order_ids": tuple(batch_order_ids)}
                    )
                    
                    # 处理每个订单并准备更新数据
                    updates = []
                    found_count = 0
                    for row in result:
                        found_count += 1
                        order_id = row[0]
                        receivable_amount = float(row[1]) if row[1] is not None else 0.0
                        
                        # 获取总结算金额和最新结算时间
                        settlement_info = settlement_dict.get(order_id)
                        if not settlement_info:
                            continue
                            
                        total_settlement, latest_settlement_time = settlement_info
                        
                        # 将时间戳字符串转换为datetime对象并格式化为日期字符串
                        payment_date = None
                        if latest_settlement_time:
                            try:
                                # 尝试解析时间戳字符串为datetime对象
                                settlement_datetime = datetime.strptime(latest_settlement_time, '%Y-%m-%d %H:%M:%S') if isinstance(latest_settlement_time, str) else datetime.fromtimestamp(latest_settlement_time)
                                payment_date = settlement_datetime.strftime('%Y-%m-%d')
                            except (ValueError, TypeError) as e:
                                print(f"  - 警告: 无法解析结算时间 '{latest_settlement_time}' 为订单 {order_id}: {str(e)}")
                        
                        # 计算待回款金额 = max(0, 应收金额 - 回款金额)
                        pending_payment = max(0.0, receivable_amount - total_settlement)
                        
                        # 添加到更新列表
                        update_data = {
                            "order_id": order_id,
                            "received_amount": total_settlement,
                            "pending_payment": pending_payment,
                            "payment_date": payment_date
                        }
                        updates.append(update_data)
                    
                    print(f"  - 在MySQL中找到 {found_count} 个匹配订单, 准备更新")
                    
                    # 如果有需要更新的记录，使用单独的连接执行更新操作
                    if updates:
                        # 使用新的连接进行更新，避免事务冲突
                        with mysql_engine.begin() as update_conn:
                            # 批量更新数据
                            print(f"  - 开始更新 {len(updates)} 条记录...")
                            batch_updated_count = 0
                            for update_data in updates:
                                # 构建更新SQL语句，根据payment_date是否为None使用不同的SQL
                                if update_data["payment_date"] is None:
                                    update_sql = """
                                    UPDATE vh_sales_data
                                    SET
                                        received_amount = :received_amount,
                                        pending_payment = :pending_payment,
                                        updated_at = CURRENT_TIMESTAMP
                                    WHERE 
                                        order_id = :order_id
                                    """
                                else:
                                    update_sql = """
                                    UPDATE vh_sales_data
                                    SET
                                        received_amount = :received_amount,
                                        pending_payment = :pending_payment,
                                        payment_date = :payment_date,
                                        updated_at = CURRENT_TIMESTAMP
                                    WHERE 
                                        order_id = :order_id
                                    """
                                
                                update_conn.execute(text(update_sql), update_data)
                                batch_updated_count += 1
                            
                            updated_count += batch_updated_count
                            batch_time = time.time() - batch_start_time
                            print(f"  - 批次 {batch_num} 已完成: 更新了 {batch_updated_count} 条记录, 耗时 {batch_time:.2f} 秒")
                    else:
                        print("  - 没有找到需要更新的记录")
            
            # 计算总运行时间
            total_time = time.time() - start_time
            found_percent = (updated_count / total_settlements * 100) if total_settlements > 0 else 0
            
            print("\n" + "=" * 60)
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行完成:")
            print(f"- 结算记录总数: {total_settlements}")
            print(f"- 成功更新记录数: {updated_count} ({found_percent:.1f}%)")
            print(f"- 未找到对应记录数: {total_settlements - updated_count}")
            print(f"- 总耗时: {total_time:.2f} 秒")
            print("=" * 60)
            
            return updated_count
            
        except Exception as e:
            error_msg = f"更新从结算记录计算的应收金额失败: {str(e)}"
            print(f"\n\n{error_msg}")
            raise Exception(error_msg)

    def get_connection(self):
        """获取SQLite数据库连接"""
        return self.orders_manager.get_connection()
