from typing import Dict, Any, Optional, List
import sqlite3
from .base_db_manager import BaseDBManager
from .table_fields import TableFields


class SettlementManager(BaseDBManager):
    """结算单管理类"""

    def _initialize_database(self):
        """
        初始化数据库表
        创建结算单表，包含订单ID、平台名称、结算时间、结算金额、结算类型、收款渠道等字段
        """
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS settlements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id TEXT NOT NULL,
            platform_id TEXT NOT NULL,
            platform_name TEXT NOT NULL,
            platform_type TEXT NOT NULL,
            settlement_time TIMESTAMP NOT NULL,
            settlement_amount REAL NOT NULL,
            settlement_type TEXT NOT NULL,
            settlement_channel TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(create_table_sql)
                # 创建索引以提高查询性能
                cursor.execute(
                    'CREATE INDEX IF NOT EXISTS idx_settlement_order_id ON settlements(order_id)')
                cursor.execute(
                    'CREATE INDEX IF NOT EXISTS idx_settlement_platform ON settlements(platform_id, platform_type, platform_name)')
                cursor.execute(
                    'CREATE INDEX IF NOT EXISTS idx_settlement_time ON settlements(settlement_time)')
                conn.commit()
        except Exception as e:
            print(f"初始化结算单表失败: {e}")

    def insert_data(self, data: Dict[TableFields, Any]) -> bool:
        """
        插入结算单数据

        Args:
            data (Dict[TableFields, Any]): 结算单数据，包含必要字段

        Returns:
            bool: 插入是否成功
        """
        required_fields = {
            TableFields.ORDER_ID.value,
            TableFields.PLATFORM_ID.value,
            TableFields.PLATFORM_NAME.value,
            TableFields.PLATFORM_TYPE.value,
            TableFields.SETTLEMENT_TIME.value,
            TableFields.SETTLEMENT_AMOUNT.value,
            TableFields.SETTLEMENT_TYPE.value,
            TableFields.SETTLEMENT_CHANNEL.value
        }

        try:
            # 规范化数据字段
            normalized_data = self._normalize_keys(data, required_fields)

            # 检查必要字段
            missing_fields = required_fields - set(normalized_data.keys())
            if missing_fields:
                raise ValueError(f"缺少必要字段: {missing_fields}")

            # 构建SQL语句
            fields = ', '.join(normalized_data.keys())
            placeholders = ', '.join(['?' for _ in normalized_data])
            values = tuple(normalized_data.values())

            insert_sql = f"""
            INSERT INTO settlements ({fields})
            VALUES ({placeholders})
            """

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(insert_sql, values)
                conn.commit()
                return True

        except Exception as e:
            print(f"插入结算单数据失败: {e}")
            return False

    def insert_batch_data(self, data: List[Dict[TableFields, Any]]) -> bool:
        """
        批量插入结算单数据
        """
        # 使用列表来保持字段顺序
        required_fields = [
            TableFields.ORDER_ID.value,
            TableFields.PLATFORM_ID.value,
            TableFields.PLATFORM_NAME.value,
            TableFields.PLATFORM_TYPE.value,
            TableFields.SETTLEMENT_TIME.value,
            TableFields.SETTLEMENT_AMOUNT.value,
            TableFields.SETTLEMENT_TYPE.value,
            TableFields.SETTLEMENT_CHANNEL.value
        ]

        try:
            # 规范化数据字段
            normalized_data_list = []
            for data_item in data:
                normalized_data = self._normalize_keys(
                    data_item, set(required_fields))

                # 检查必要字段
                missing_fields = set(required_fields) - \
                    set(normalized_data.keys())
                if missing_fields:
                    raise ValueError(f"缺少必要字段: {missing_fields}")

                # 按照定义的字段顺序构建数据元组
                row_data = [normalized_data[field]
                            for field in required_fields]
                normalized_data_list.append(tuple(row_data))

            # 构建SQL语句
            fields = ', '.join(required_fields)
            placeholders = ', '.join(['?' for _ in required_fields])

            insert_sql = f"""
            INSERT INTO settlements ({fields})
            VALUES ({placeholders})
            """

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.executemany(insert_sql, normalized_data_list)
                conn.commit()
                return True

        except Exception as e:
            print(f"批量插入结算单数据失败: {e}")
            return False

    def query_data(self, page: int = 1, page_size: int = 10,
                   filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        查询结算单数据，支持分页和过滤

        Args:
            page (int): 页码，从1开始
            page_size (int): 每页记录数
            filters (Optional[Dict[str, Any]]): 过滤条件，支持：
                - order_id: 订单ID（精确匹配）
                - platform_type: 平台类型（精确匹配）
                - platform_name: 平台名称（精确匹配）
                - settlement_type: 结算类型（精确匹配）

        Returns:
            Dict[str, Any]: 查询结果，包含分页信息和数据
        """
        try:
            # 构建查询条件
            where_clauses = []
            params = []

            if filters:
                # 精确匹配字段
                exact_match_fields = [
                    'order_id', 'platform_id', 'platform_type', 'platform_name',
                    'settlement_type'
                ]
                for field in exact_match_fields:
                    if field in filters and filters[field]:
                        where_clauses.append(f'{field} = ?')
                        params.append(filters[field])

            # 组装WHERE子句
            where_clause = ''
            if where_clauses:
                where_clause = ' WHERE ' + ' AND '.join(where_clauses)

            # 添加排序
            order_clause = ' ORDER BY settlement_time DESC, created_at DESC'

            base_query = 'SELECT * FROM settlements'
            query = base_query + where_clause + order_clause

            return self.paginate_query(query, page, page_size, '', params)

        except Exception as e:
            print(f"查询结算单数据失败: {e}")
            return {'error': str(e)}
