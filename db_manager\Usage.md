## 订单管理

```python
# 初始化订单管理器
from data_folder_utils import Folder<PERSON>anager
from db_manager import <PERSON><PERSON>ana<PERSON>, SettlementManager, TableFields

orders_manager = OrdersManager(FolderManager.get_db_file_path())

# 插入订单数据示例
insert_data = {
    TableFields.ORDER_ID: "order002",
    TableFields.PLATFORM_TYPE: PlatformType.DOUYIN.value,
    TableFields.PLATFORM_NAME: PlatformName.DOUYIN_JIUYUN_FLAGSHIP.value,
    TableFields.MERCHANT_RECEIVABLE: 1000.00,
    TableFields.ORDER_AMOUNT: 1200.00,
    TableFields.RECEIVABLE_AMOUNT: 1000.00,
    TableFields.PAYMENT_CHANNEL: "alipay",
    TableFields.PAYMENT_DATE: "2024-03-25"
}
success = orders_manager.insert_data(insert_data)

# 更新订单数据示例
update_data = {
    TableFields.RECEIVED_AMOUNT: 800.00,
    TableFields.PENDING_PAYMENT: 200.00,
    TableFields.PAYMENT_DATE: "2024-03-26"
}
success = orders_manager.update_data("order001", update_data)
```

## 结算单管理

```python
settlement_manager = SettlementManager(FolderManager.get_db_file_path())
data = {
    TableFields.ORDER_ID: "order1233",
    TableFields.PLATFORM_NAME: PlatformName.DOUYIN_JIUYUN_FLAGSHIP.value,
    TableFields.PLATFORM_TYPE: PlatformType.DOUYIN.value,
    TableFields.SETTLEMENT_TIME: "2024-03-21",
    TableFields.SETTLEMENT_AMOUNT: 1000.00,
    TableFields.SETTLEMENT_TYPE: "type_a",
    TableFields.SETTLEMENT_CHANNEL: "channel_a"
}
success = settlement_manager.insert_data(data)

```

## 数据库前端页面
提供Web页面，可以查看和搜索订单和结算单数据

```python
from db_manager.web_server import WebServer
web_server = WebServer(orders_manager, settlement_manager)
web_server.run()
```

通过浏览器访问：http://localhost:5001/db 