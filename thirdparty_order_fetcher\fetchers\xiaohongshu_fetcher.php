<?php

require_once __DIR__ . '/../base_fetcher.php';
require_once __DIR__ . '/../../data_folder_utils.php';
require_once __DIR__ . '/../../db_manager.php';
require_once __DIR__ . '/../../shop_manager/constants.php';
require_once __DIR__ . '/../exceptions.php';
require_once __DIR__ . '/../models.php';

class XiaohongshuOrderFetcher extends BaseOrderFetcher
{
    // API基础URL
    const API_URL = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
    const API_VERSION = "2.0";
    
    // 小红书应用凭证配置 - 支持多个应用
    private $xhsAppConfigs = [
        'app1' => [
            'app_id' => 'e840e096a975478984d4',
            'app_secret' => 'c7931a95f48312d7918cde5de2c47b8e'
        ],
        'app2' => [
            'app_id' => '4787a2382e784ccb9b09',
            'app_secret' => '5a98d1879df35434299a7b7c62e9cb2b'
        ]
    ];
    
    // 店铺与应用的映射关系
    private $shopAppMapping = [
        '62b98b750d601800010dc853' => 'app1',
        '650a60e17fa15200013acf16' => 'app1',
        '65113b63effd830001ca90e0' => 'app2',
        '653b599dbffe730001559bd6' => 'app2'
    ];
    
    private $platformShopConfig;
    private $shops;
    private $ordersManager;
    private $shopsInfo;
    private $orderQuery;
    
    public function __construct()
    {
        parent::__construct(PlatformName::XHS_YUNWINE, PlatformType::XIAOHONGSHU);
        
        $this->platformShopConfig = new PlatformShopConfig();
        $this->shops = $this->platformShopConfig->getShopsByPlatform(PlatformType::XIAOHONGSHU);
        
        $this->ordersManager = new OrdersManager(FolderManager::getDbFilePath());
        $this->ensureOrderProcessTableExists();
        
        echo "从配置中获取到 " . count($this->shops) . " 个小红书店铺\n";
        foreach ($this->shops as $shop) {
            echo "  - " . $shop->shop_name . "\n";
        }
        
        $this->shopsInfo = $this->getAllShopsInfo();
        $this->orderQuery = new OrderQuery();
    }
    
    private function ensureOrderProcessTableExists()
    {
        $createTableSql = "
            CREATE TABLE IF NOT EXISTS order_process_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT NOT NULL,
                process_status INTEGER DEFAULT 0,
                process_date TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(order_id)
            )
        ";
        
        try {
            $this->ordersManager->executeQuery($createTableSql);
            $todayDate = date('Y-m-d');
            
            $deleteOldRecordsSql = "DELETE FROM order_process_records WHERE process_date != ?";
            $this->ordersManager->executeQuery($deleteOldRecordsSql, [$todayDate]);
            
            echo "订单处理记录表初始化成功\n";
        } catch (Exception $e) {
            echo "初始化订单处理记录表失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    public function fetchOrders($year, $month, $shopId = null)
    {
        $this->validateDate($year, $month);
        
        if (empty($this->shopsInfo)) {
            throw new PlatformRunError("小红书店铺信息未初始化");
        }
        
        $allOrders = [];
        $maxFetchIterations = 10;
        
        for ($iteration = 0; $iteration < $maxFetchIterations; $iteration++) {
            if ($iteration > 0) {
                echo "第 " . ($iteration + 1) . " 次尝试获取待处理订单...\n";
            }
            
            $shopsToProcess = $shopId ? 
                array_filter($this->shops, function($s) use ($shopId) { return $s->shop_id === $shopId; }) : 
                $this->shops;
            
            $currentIterationOrders = [];
            
            foreach ($shopsToProcess as $shop) {
                try {
                    $shopName = $shop->shop_name;
                    echo "正在获取店铺 {$shopName} 的订单...\n";
                    
                    $shopOrders = $this->fetchOrdersForMonth($shop, $year, $month);
                    $currentIterationOrders = array_merge($currentIterationOrders, $shopOrders);
                    echo "成功获取 " . count($shopOrders) . " 个订单\n";
                    
                } catch (Exception $e) {
                    echo "错误: 获取店铺 {$shop->shop_id} 订单时出错: " . $e->getMessage() . "\n";
                }
            }
            
            $allOrders = array_merge($allOrders, $currentIterationOrders);
            
            $hasPendingOrders = $this->checkPendingOrders($year, $month, $shopId);
            
            if (!$hasPendingOrders || empty($currentIterationOrders)) {
                break;
            }
            
            if ($iteration < $maxFetchIterations - 1 && $hasPendingOrders) {
                echo "还有订单待处理，5秒后将进行下一轮获取...\n";
                sleep(5);
            }
        }
        
        echo "所有轮次共获取处理了 " . count($allOrders) . " 个订单\n";
        return $allOrders;
    }
    
    private function fetchOrdersForMonth($shop, $year, $month)
    {
        $startDate = new DateTime("{$year}-{$month}-01");
        $endDate = clone $startDate;
        $endDate->modify('last day of this month')->setTime(23, 59, 59);
        
        $timeWindows = [];
        $currentDate = clone $startDate;
        
        while ($currentDate < $endDate) {
            $windowEnd = min(clone $currentDate->modify('+24 hours'), $endDate);
            $timeWindows[] = [
                $currentDate->getTimestamp(),
                $windowEnd->getTimestamp()
            ];
            $currentDate = clone $windowEnd->modify('+1 second');
        }
        
        $allOrderIds = [];
        foreach ($timeWindows as [$startTime, $endTime]) {
            try {
                $windowOrderIds = $this->fetchOrderIdsForWindow($shop, $startTime, $endTime);
                $allOrderIds = array_merge($allOrderIds, $windowOrderIds);
            } catch (Exception $e) {
                echo "获取时间窗口订单失败: " . $e->getMessage() . "\n";
                sleep(1);
            }
        }
        
        return $this->fetchOrderDetails($shop, array_unique($allOrderIds));
    }
    
    private function callApi($shop, $method, $bizParams)
    {
        $timestamp = (string)time();
        
        $requestBody = array_merge([
            'appId' => $shop->app_id,
            'timestamp' => $timestamp,
            'version' => self::API_VERSION,
            'method' => $method
        ], $bizParams);
        
        $sign = $this->calculateSign($method, $shop->app_id, $timestamp, self::API_VERSION, $shop->app_secret);
        $requestBody['sign'] = $sign;
        
        if (isset($shop->access_token) && $shop->access_token) {
            $requestBody['accessToken'] = $shop->access_token;
        }
        
        $maxRetries = 3;
        $retryDelay = 1.0;
        
        for ($retryCount = 0; $retryCount < $maxRetries; $retryCount++) {
            try {
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => self::API_URL,
                    CURLOPT_POST => true,
                    CURLOPT_POSTFIELDS => json_encode($requestBody),
                    CURLOPT_HTTPHEADER => ['Content-Type: application/json;charset=utf-8'],
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 60
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode !== 200) {
                    throw new APIConnectionError("HTTP请求失败，状态码: {$httpCode}");
                }
                
                $data = json_decode($response, true);
                
                if ($data['error_code'] !== 0) {
                    $errorMsg = $data['error_msg'] ?? '未知错误';
                    throw new PlatformRunError("API错误: {$errorMsg}");
                }
                
                return $data['data'] ?? [];
                
            } catch (Exception $e) {
                if ($retryCount < $maxRetries - 1) {
                    sleep($retryDelay);
                    $retryDelay *= 2;
                } else {
                    throw $e;
                }
            }
        }
    }
    
    private function calculateSign($method, $appId, $timestamp, $version, $appSecret)
    {
        $paramStr = "{$method}?appId={$appId}&timestamp={$timestamp}&version={$version}";
        $signStr = $paramStr . $appSecret;
        return md5($signStr);
    }
    
    // ... 其他方法的实现
}