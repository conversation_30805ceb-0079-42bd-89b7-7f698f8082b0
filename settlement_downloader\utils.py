from datetime import datetime
import os

def validate_date_range(start_date: datetime, end_date: datetime) -> None:
    """验证日期范围是否有效"""
    if start_date > end_date:
        raise ValueError("start_date must be earlier than end_date")

def ensure_directory_exists(path: str) -> None:
    """确保保存路径的目录存在"""
    directory = os.path.dirname(path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)